Running with gitlab-runner 16.6.1 (f5da3c5a)
  on symmy-production.symmy.app qqzqyozvc, system ID: s_ff7f309fd2bb
Preparing the "shell" executor 00:00
Using Shell (bash) executor...
Preparing environment 00:00
Running on symmy-production.symmy.app...
Getting source from Git repository 00:00
Fetching changes with git depth set to 20...
Reinitialized existing Git repository in /home/<USER>/builds/qqzqyozvc/0/symmy/symmy/.git/
Checking out c6355010 as detached HEAD (ref is main)...
Removing .env
Removing src/backend/.env
Skipping Git submodules setup
Executing "step_script" stage of the job script 01:15
$ echo "$PROD_ENV" > src/frontend/.env
$ docker run --rm docker.whys.eu/symmy/symmy:frontend-prod sh -c "npm run test:no-watch"
> symmy-frontend@1.0.0 test:no-watch
> cross-env ASYNC_UTIL_TIMEOUT=10000 vitest run --test-timeout=20000
/bin/sh: git: not found
[sentry-vite-plugin] Warning: No release name provided. Will not inject release. Please set the `release.name` option to identify your release.
[sentry-vite-plugin] Warning: No release name provided. Will not create release. Please set the `release.name` option to identify your release.
[sentry-vite-plugin] Info: Sending telemetry data on issues and performance to Sentry. To disable telemetry, set `options.telemetry` to `false`.
 RUN  v0.33.0 /app
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should display user data error
Warning: `ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info.
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/pages/ResetPasswordPage.test.tsx > ResetPasswordPage > should reset password
Warning: `ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info.
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/pages/LoginPage.test.tsx > LoginPage > should not log in
Warning: `ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info.
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should change organization
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/pages/ForgotPasswordPage.test.tsx > ForgotPasswordPage > should reset password
Warning: `ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info.
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/pages/LoginPage.test.tsx > LoginPage > should not log in
AxiosError: Request failed with status code 400
    at settle (file:///app/node_modules/axios/lib/core/settle.js:19:12)
    at XMLHttpRequest.onloadend (file:///app/node_modules/axios/lib/adapters/xhr.js:107:7)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at finalizeResponse (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:329:12)
    at XMLHttpRequestController.respondWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:364:7)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:120:39) {
  code: 'ERR_BAD_REQUEST',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/users/authenticate/',
    data: '{"email":"<EMAIL>","password":"wrong-password"}',
    params: {},
    method: 'post'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function (anonymous)],
    getAllResponseHeaders: [Function (anonymous)],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: 'http://localhost:3000/api/v1/users/authenticate/',
    status: 400,
    statusText: 'Bad Request',
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4,
    [Symbol(isMockedResponse)]: true
  },
  response: {
    data: '',
    status: 400,
    statusText: 'Bad Request',
    headers: AxiosHeaders {},
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [AxiosHeaders],
      url: '/api/v1/users/authenticate/',
      data: '{"email":"<EMAIL>","password":"wrong-password"}',
      params: {},
      method: 'post'
    },
    request: XMLHttpRequest {
      open: [Function: open],
      setRequestHeader: [Function: setRequestHeader],
      send: [Function: send],
      abort: [Function: abort],
      getResponseHeader: [Function (anonymous)],
      getAllResponseHeaders: [Function (anonymous)],
      overrideMimeType: [Function: overrideMimeType],
      onreadystatechange: [Getter/Setter],
      readyState: 4,
      timeout: [Getter/Setter],
      withCredentials: [Getter/Setter],
      upload: [Getter],
      responseURL: 'http://localhost:3000/api/v1/users/authenticate/',
      status: 400,
      statusText: 'Bad Request',
      responseType: [Getter/Setter],
      response: [Getter],
      responseText: [Getter],
      responseXML: [Getter],
      UNSENT: 0,
      OPENED: 1,
      HEADERS_RECEIVED: 2,
      LOADING: 3,
      DONE: 4,
      [Symbol(isMockedResponse)]: true
    }
  }
}
stderr | src/pages/ForgotPasswordPage.test.tsx > ForgotPasswordPage > should display reset password error
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/pages/LoginPage.test.tsx > LoginPage > should log in
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/pages/ForgotPasswordPage.test.tsx > ForgotPasswordPage > should display reset password error
AxiosError: Request failed with status code 500
    at settle (file:///app/node_modules/axios/lib/core/settle.js:19:12)
    at XMLHttpRequest.onloadend (file:///app/node_modules/axios/lib/adapters/xhr.js:107:7)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at finalizeResponse (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:329:12)
    at XMLHttpRequestController.respondWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:364:7)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:120:39) {
  code: 'ERR_BAD_RESPONSE',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/users/forgot-password/',
    data: '{"email":"<EMAIL>"}',
    params: {},
    method: 'post'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function (anonymous)],
    getAllResponseHeaders: [Function (anonymous)],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: 'http://localhost:3000/api/v1/users/forgot-password/',
    status: 500,
    statusText: 'Internal Server Error',
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4,
    [Symbol(isMockedResponse)]: true
  },
  response: {
    data: '',
    status: 500,
    statusText: 'Internal Server Error',
    headers: AxiosHeaders {},
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [AxiosHeaders],
      url: '/api/v1/users/forgot-password/',
      data: '{"email":"<EMAIL>"}',
      params: {},
      method: 'post'
    },
    request: XMLHttpRequest {
      open: [Function: open],
      setRequestHeader: [Function: setRequestHeader],
      send: [Function: send],
      abort: [Function: abort],
      getResponseHeader: [Function (anonymous)],
      getAllResponseHeaders: [Function (anonymous)],
      overrideMimeType: [Function: overrideMimeType],
      onreadystatechange: [Getter/Setter],
      readyState: 4,
      timeout: [Getter/Setter],
      withCredentials: [Getter/Setter],
      upload: [Getter],
      responseURL: 'http://localhost:3000/api/v1/users/forgot-password/',
      status: 500,
      statusText: 'Internal Server Error',
      responseType: [Getter/Setter],
      response: [Getter],
      responseText: [Getter],
      responseXML: [Getter],
      UNSENT: 0,
      OPENED: 1,
      HEADERS_RECEIVED: 2,
      LOADING: 3,
      DONE: 4,
      [Symbol(isMockedResponse)]: true
    }
  }
}
 ✓ src/pages/ForgotPasswordPage.test.tsx  (2 tests) 3619ms
stdout | src/pages/AnalyticsPage.test.tsx > AnalyticsPage > should render correctly
value: null
value: null
value: null
value: null
value: null
value: null
value: null
value: null
stderr | src/pages/AnalyticsPage.test.tsx > AnalyticsPage > should render correctly
Warning: `ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info.
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
 ✓ src/pages/LoginPage.test.tsx  (2 tests) 4141ms
stdout | src/pages/ProfilePage.test.tsx > ProfilePage > should render user data
value: null
value: null
stderr | src/pages/ProfilePage.test.tsx > ProfilePage > should render user data
Warning: `ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info.
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/pages/FlowCallPage.test.tsx > FlowCallPage > should render schema and node calls table
Warning: `ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info.
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stdout | src/pages/FlowCallPage.test.tsx > FlowCallPage > should render schema and node calls table
value: null
value: null
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should change organization
<empty line>
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should change organization
value: null
value: null
value: null
value: null
value: null
value: null
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should change organization
<empty line>
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should change organization
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stdout | src/pages/ProfilePage.test.tsx > ProfilePage > should remove and upload avatar
value: null
value: null
stderr | src/pages/ProfilePage.test.tsx > ProfilePage > should remove and upload avatar
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32)
    at runNextTicks (node:internal/process/task_queues:60:5)
    at listOnTimeout (node:internal/timers:545:9)
    at processTimers (node:internal/timers:519:7) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/pages/AnalyticsPage.test.tsx > AnalyticsPage > should render correctly
<empty line>
stdout | src/pages/AnalyticsPage.test.tsx > AnalyticsPage > should render correctly
newValue: projects
JSON.stringify(newValue): "projects"
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should change organization
<empty line>
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should change organization
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/ResetPasswordPage.test.tsx > ResetPasswordPage > should reset password
<empty line>
stdout | src/pages/ResetPasswordPage.test.tsx > ResetPasswordPage > should reset password
value: null
value: null
value: null
value: null
value: null
value: null
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should change organization
<empty line>
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should change organization
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/AnalyticsPage.test.tsx > AnalyticsPage > should render correctly
<empty line>
stdout | src/pages/AnalyticsPage.test.tsx > AnalyticsPage > should render correctly
newValue: weekly
JSON.stringify(newValue): "weekly"
stderr | src/pages/AnalyticsPage.test.tsx > AnalyticsPage > should render correctly
<empty line>
stdout | src/pages/AnalyticsPage.test.tsx > AnalyticsPage > should render correctly
newValue: monthly
JSON.stringify(newValue): "monthly"
stderr | src/pages/ResetPasswordPage.test.tsx > ResetPasswordPage > should display password reset error
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/pages/ResetPasswordPage.test.tsx > ResetPasswordPage > should display password reset error
AxiosError: Request failed with status code 500
    at settle (file:///app/node_modules/axios/lib/core/settle.js:19:12)
    at XMLHttpRequest.onloadend (file:///app/node_modules/axios/lib/adapters/xhr.js:107:7)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at finalizeResponse (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:329:12)
    at XMLHttpRequestController.respondWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:364:7)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:120:39) {
  code: 'ERR_BAD_RESPONSE',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/users/reset-password/correct-uuid/correct-token/',
    data: '{"logout":false,"password1":"new-password","password2":"new-password"}',
    params: {},
    method: 'patch'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function (anonymous)],
    getAllResponseHeaders: [Function (anonymous)],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: 'http://localhost:3000/api/v1/users/reset-password/correct-uuid/correct-token/',
    status: 500,
    statusText: 'Internal Server Error',
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4,
    [Symbol(isMockedResponse)]: true
  },
  response: {
    data: '',
    status: 500,
    statusText: 'Internal Server Error',
    headers: AxiosHeaders {},
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [AxiosHeaders],
      url: '/api/v1/users/reset-password/correct-uuid/correct-token/',
      data: '{"logout":false,"password1":"new-password","password2":"new-password"}',
      params: {},
      method: 'patch'
    },
    request: XMLHttpRequest {
      open: [Function: open],
      setRequestHeader: [Function: setRequestHeader],
      send: [Function: send],
      abort: [Function: abort],
      getResponseHeader: [Function (anonymous)],
      getAllResponseHeaders: [Function (anonymous)],
      overrideMimeType: [Function: overrideMimeType],
      onreadystatechange: [Getter/Setter],
      readyState: 4,
      timeout: [Getter/Setter],
      withCredentials: [Getter/Setter],
      upload: [Getter],
      responseURL: 'http://localhost:3000/api/v1/users/reset-password/correct-uuid/correct-token/',
      status: 500,
      statusText: 'Internal Server Error',
      responseType: [Getter/Setter],
      response: [Getter],
      responseText: [Getter],
      responseXML: [Getter],
      UNSENT: 0,
      OPENED: 1,
      HEADERS_RECEIVED: 2,
      LOADING: 3,
      DONE: 4,
      [Symbol(isMockedResponse)]: true
    }
  }
}
stdout | src/pages/FlowCallPage.test.tsx > FlowCallPage > should display flow call error
value: null
value: null
stderr | src/pages/FlowCallPage.test.tsx > FlowCallPage > should display flow call error
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32)
    at runNextTicks (node:internal/process/task_queues:60:5)
    at listOnTimeout (node:internal/timers:545:9)
    at processTimers (node:internal/timers:519:7) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
AxiosError: Request failed with status code 500
    at settle (file:///app/node_modules/axios/lib/core/settle.js:19:12)
    at XMLHttpRequest.onloadend (file:///app/node_modules/axios/lib/adapters/xhr.js:107:7)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at finalizeResponse (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:329:12)
    at XMLHttpRequestController.respondWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:364:7)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:120:39)
    at processTicksAndRejections (node:internal/process/task_queues:95:5) {
  code: 'ERR_BAD_RESPONSE',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/analytics/flows/node-calls/?mongo_flow_ids=flow-call-uuid-1&bulk_result=True',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function (anonymous)],
    getAllResponseHeaders: [Function (anonymous)],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: 'http://localhost:3000/api/v1/analytics/flows/node-calls/?mongo_flow_ids=flow-call-uuid-1&bulk_result=True',
    status: 500,
    statusText: 'Internal Server Error',
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4,
    [Symbol(isMockedResponse)]: true
  },
  response: {
    data: '',
    status: 500,
    statusText: 'Internal Server Error',
    headers: AxiosHeaders {},
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [AxiosHeaders],
      url: '/api/v1/analytics/flows/node-calls/?mongo_flow_ids=flow-call-uuid-1&bulk_result=True',
      data: '{}',
      params: {},
      method: 'get'
    },
    request: XMLHttpRequest {
      open: [Function: open],
      setRequestHeader: [Function: setRequestHeader],
      send: [Function: send],
      abort: [Function: abort],
      getResponseHeader: [Function (anonymous)],
      getAllResponseHeaders: [Function (anonymous)],
      overrideMimeType: [Function: overrideMimeType],
      onreadystatechange: [Getter/Setter],
      readyState: 4,
      timeout: [Getter/Setter],
      withCredentials: [Getter/Setter],
      upload: [Getter],
      responseURL: 'http://localhost:3000/api/v1/analytics/flows/node-calls/?mongo_flow_ids=flow-call-uuid-1&bulk_result=True',
      status: 500,
      statusText: 'Internal Server Error',
      responseType: [Getter/Setter],
      response: [Getter],
      responseText: [Getter],
      responseXML: [Getter],
      UNSENT: 0,
      OPENED: 1,
      HEADERS_RECEIVED: 2,
      LOADING: 3,
      DONE: 4,
      [Symbol(isMockedResponse)]: true
    }
  }
}
stderr | src/pages/ResetPasswordPage.test.tsx > ResetPasswordPage > should not reset password due to password mismatch
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/pages/AnalyticsPage.test.tsx > AnalyticsPage > should render correctly
<empty line>
stdout | src/pages/AnalyticsPage.test.tsx > AnalyticsPage > should render correctly
newValue: [
  Moment<2025-05-01T00:00:00+00:00>,
  Moment<2025-05-28T00:00:00+00:00>
]
JSON.stringify(newValue): ["2025-05-01T00:00:00.000Z","2025-05-28T00:00:00.000Z"]
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should change organization
<empty line>
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should change organization
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/ResetPasswordPage.test.tsx > ResetPasswordPage > should not reset password due to wrong uuid and token
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/pages/ResetPasswordPage.test.tsx > ResetPasswordPage > should not reset password due to wrong uuid and token
AxiosError: Request failed with status code 400
    at settle (file:///app/node_modules/axios/lib/core/settle.js:19:12)
    at XMLHttpRequest.onloadend (file:///app/node_modules/axios/lib/adapters/xhr.js:107:7)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at finalizeResponse (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:329:12)
    at XMLHttpRequestController.respondWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:364:7)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:120:39) {
  code: 'ERR_BAD_REQUEST',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/users/reset-password/wrong-uuid/wrong-token/',
    data: '{"logout":false,"password1":"new-password","password2":"new-password"}',
    params: {},
    method: 'patch'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function (anonymous)],
    getAllResponseHeaders: [Function (anonymous)],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: 'http://localhost:3000/api/v1/users/reset-password/wrong-uuid/wrong-token/',
    status: 400,
    statusText: 'Bad Request',
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4,
    [Symbol(isMockedResponse)]: true
  },
  response: {
    data: '',
    status: 400,
    statusText: 'Bad Request',
    headers: AxiosHeaders {},
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [AxiosHeaders],
      url: '/api/v1/users/reset-password/wrong-uuid/wrong-token/',
      data: '{"logout":false,"password1":"new-password","password2":"new-password"}',
      params: {},
      method: 'patch'
    },
    request: XMLHttpRequest {
      open: [Function: open],
      setRequestHeader: [Function: setRequestHeader],
      send: [Function: send],
      abort: [Function: abort],
      getResponseHeader: [Function (anonymous)],
      getAllResponseHeaders: [Function (anonymous)],
      overrideMimeType: [Function: overrideMimeType],
      onreadystatechange: [Getter/Setter],
      readyState: 4,
      timeout: [Getter/Setter],
      withCredentials: [Getter/Setter],
      upload: [Getter],
      responseURL: 'http://localhost:3000/api/v1/users/reset-password/wrong-uuid/wrong-token/',
      status: 400,
      statusText: 'Bad Request',
      responseType: [Getter/Setter],
      response: [Getter],
      responseText: [Getter],
      responseXML: [Getter],
      UNSENT: 0,
      OPENED: 1,
      HEADERS_RECEIVED: 2,
      LOADING: 3,
      DONE: 4,
      [Symbol(isMockedResponse)]: true
    }
  }
}
 ✓ src/pages/ResetPasswordPage.test.tsx  (4 tests) 9749ms
 ✓ src/pages/FlowCallPage.test.tsx  (2 tests) 10090ms
stdout | src/pages/ProfilePage.test.tsx > ProfilePage > should display remove avatar error
value: null
value: null
stderr | src/pages/ProfilePage.test.tsx > ProfilePage > should display remove avatar error
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
AxiosError: Request failed with status code 500
    at settle (file:///app/node_modules/axios/lib/core/settle.js:19:12)
    at XMLHttpRequest.onloadend (file:///app/node_modules/axios/lib/adapters/xhr.js:107:7)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at finalizeResponse (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:329:12)
    at XMLHttpRequestController.respondWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:364:7)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:120:39)
    at runNextTicks (node:internal/process/task_queues:60:5)
    at listOnTimeout (node:internal/timers:545:9) {
  code: 'ERR_BAD_RESPONSE',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/users/remove-avatar/',
    data: '{}',
    params: {},
    method: 'delete'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function (anonymous)],
    getAllResponseHeaders: [Function (anonymous)],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: 'http://localhost:3000/api/v1/users/remove-avatar/',
    status: 500,
    statusText: 'Internal Server Error',
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4,
    [Symbol(isMockedResponse)]: true
  },
  response: {
    data: '',
    status: 500,
    statusText: 'Internal Server Error',
    headers: AxiosHeaders {},
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [AxiosHeaders],
      url: '/api/v1/users/remove-avatar/',
      data: '{}',
      params: {},
      method: 'delete'
    },
    request: XMLHttpRequest {
      open: [Function: open],
      setRequestHeader: [Function: setRequestHeader],
      send: [Function: send],
      abort: [Function: abort],
      getResponseHeader: [Function (anonymous)],
      getAllResponseHeaders: [Function (anonymous)],
      overrideMimeType: [Function: overrideMimeType],
      onreadystatechange: [Getter/Setter],
      readyState: 4,
      timeout: [Getter/Setter],
      withCredentials: [Getter/Setter],
      upload: [Getter],
      responseURL: 'http://localhost:3000/api/v1/users/remove-avatar/',
      status: 500,
      statusText: 'Internal Server Error',
      responseType: [Getter/Setter],
      response: [Getter],
      responseText: [Getter],
      responseXML: [Getter],
      UNSENT: 0,
      OPENED: 1,
      HEADERS_RECEIVED: 2,
      LOADING: 3,
      DONE: 4,
      [Symbol(isMockedResponse)]: true
    }
  }
}
stdout | src/pages/AnalyticsPage.test.tsx > AnalyticsPage > should display analytics error
value: null
value: null
value: null
value: null
value: null
value: null
value: null
value: null
stderr | src/pages/AnalyticsPage.test.tsx > AnalyticsPage > should display analytics error
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
AxiosError: Request failed with status code 500
    at settle (file:///app/node_modules/axios/lib/core/settle.js:19:12)
    at XMLHttpRequest.onloadend (file:///app/node_modules/axios/lib/adapters/xhr.js:107:7)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at finalizeResponse (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:329:12)
    at XMLHttpRequestController.respondWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:364:7)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:120:39) {
  code: 'ERR_BAD_RESPONSE',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/analytics/organizations/node-calls/?bulk_result=True&organization_uuids=organization-uuid-1&date_from=2025-05-01T00:00:00&date_to=2025-05-31T23:59:59',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function (anonymous)],
    getAllResponseHeaders: [Function (anonymous)],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: 'http://localhost:3000/api/v1/analytics/organizations/node-calls/?bulk_result=True&organization_uuids=organization-uuid-1&date_from=2025-05-01T00:00:00&date_to=2025-05-31T23:59:59',
    status: 500,
    statusText: 'Internal Server Error',
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4,
    [Symbol(isMockedResponse)]: true
  },
  response: {
    data: '',
    status: 500,
    statusText: 'Internal Server Error',
    headers: AxiosHeaders {},
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [AxiosHeaders],
      url: '/api/v1/analytics/organizations/node-calls/?bulk_result=True&organization_uuids=organization-uuid-1&date_from=2025-05-01T00:00:00&date_to=2025-05-31T23:59:59',
      data: '{}',
      params: {},
      method: 'get'
    },
    request: XMLHttpRequest {
      open: [Function: open],
      setRequestHeader: [Function: setRequestHeader],
      send: [Function: send],
      abort: [Function: abort],
      getResponseHeader: [Function (anonymous)],
      getAllResponseHeaders: [Function (anonymous)],
      overrideMimeType: [Function: overrideMimeType],
      onreadystatechange: [Getter/Setter],
      readyState: 4,
      timeout: [Getter/Setter],
      withCredentials: [Getter/Setter],
      upload: [Getter],
      responseURL: 'http://localhost:3000/api/v1/analytics/organizations/node-calls/?bulk_result=True&organization_uuids=organization-uuid-1&date_from=2025-05-01T00:00:00&date_to=2025-05-31T23:59:59',
      status: 500,
      statusText: 'Internal Server Error',
      responseType: [Getter/Setter],
      response: [Getter],
      responseText: [Getter],
      responseXML: [Getter],
      UNSENT: 0,
      OPENED: 1,
      HEADERS_RECEIVED: 2,
      LOADING: 3,
      DONE: 4,
      [Symbol(isMockedResponse)]: true
    }
  }
}
 ✓ src/pages/AnalyticsPage.test.tsx  (2 tests) 11379ms
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should change project
value: null
value: null
value: null
value: null
value: null
value: null
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should change project
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should change project
<empty line>
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should change project
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stdout | src/pages/ProfilePage.test.tsx > ProfilePage > should display upload avatar error
value: null
value: null
stderr | src/pages/ProfilePage.test.tsx > ProfilePage > should display upload avatar error
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stdout | src/pages/ProfilePage.test.tsx > ProfilePage > should display upload avatar error
<empty line>
stderr | src/pages/ProfilePage.test.tsx > ProfilePage > should display upload avatar error
AxiosError: Request failed with status code 500
    at settle (file:///app/node_modules/axios/lib/core/settle.js:19:12)
    at XMLHttpRequest.onloadend (file:///app/node_modules/axios/lib/adapters/xhr.js:107:7)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at finalizeResponse (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:329:12)
    at XMLHttpRequestController.respondWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:364:7)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:120:39) {
  code: 'ERR_BAD_RESPONSE',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'multipart/form-data'
    },
    url: '/api/v1/users/update-avatar/',
    data: FormData {},
    params: {},
    method: 'patch'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function (anonymous)],
    getAllResponseHeaders: [Function (anonymous)],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: 'http://localhost:3000/api/v1/users/update-avatar/',
    status: 500,
    statusText: 'Internal Server Error',
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4,
    [Symbol(isMockedResponse)]: true
  },
  response: {
    data: '',
    status: 500,
    statusText: 'Internal Server Error',
    headers: AxiosHeaders {},
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [AxiosHeaders],
      url: '/api/v1/users/update-avatar/',
      data: FormData {},
      params: {},
      method: 'patch'
    },
    request: XMLHttpRequest {
      open: [Function: open],
      setRequestHeader: [Function: setRequestHeader],
      send: [Function: send],
      abort: [Function: abort],
      getResponseHeader: [Function (anonymous)],
      getAllResponseHeaders: [Function (anonymous)],
      overrideMimeType: [Function: overrideMimeType],
      onreadystatechange: [Getter/Setter],
      readyState: 4,
      timeout: [Getter/Setter],
      withCredentials: [Getter/Setter],
      upload: [Getter],
      responseURL: 'http://localhost:3000/api/v1/users/update-avatar/',
      status: 500,
      statusText: 'Internal Server Error',
      responseType: [Getter/Setter],
      response: [Getter],
      responseText: [Getter],
      responseXML: [Getter],
      UNSENT: 0,
      OPENED: 1,
      HEADERS_RECEIVED: 2,
      LOADING: 3,
      DONE: 4,
      [Symbol(isMockedResponse)]: true
    }
  }
}
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should change flow
value: null
value: null
value: null
value: null
value: null
value: null
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should change flow
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stdout | src/pages/ProfilePage.test.tsx > ProfilePage > should update user data
value: null
value: null
stderr | src/pages/ProfilePage.test.tsx > ProfilePage > should update user data
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should change flow
<empty line>
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should change flow
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should display organizations error
value: null
value: null
value: null
value: null
value: null
value: null
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should display organizations error
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
AxiosError: Request failed with status code 500
    at settle (file:///app/node_modules/axios/lib/core/settle.js:19:12)
    at XMLHttpRequest.onloadend (file:///app/node_modules/axios/lib/adapters/xhr.js:107:7)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at finalizeResponse (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:329:12)
    at XMLHttpRequestController.respondWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:364:7)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:120:39) {
  code: 'ERR_BAD_RESPONSE',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/organizations/list/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function (anonymous)],
    getAllResponseHeaders: [Function (anonymous)],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: 'http://localhost:3000/api/v1/core/organizations/list/',
    status: 500,
    statusText: 'Internal Server Error',
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4,
    [Symbol(isMockedResponse)]: true
  },
  response: {
    data: '',
    status: 500,
    statusText: 'Internal Server Error',
    headers: AxiosHeaders {},
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [AxiosHeaders],
      url: '/api/v1/core/organizations/list/',
      data: '{}',
      params: {},
      method: 'get'
    },
    request: XMLHttpRequest {
      open: [Function: open],
      setRequestHeader: [Function: setRequestHeader],
      send: [Function: send],
      abort: [Function: abort],
      getResponseHeader: [Function (anonymous)],
      getAllResponseHeaders: [Function (anonymous)],
      overrideMimeType: [Function: overrideMimeType],
      onreadystatechange: [Getter/Setter],
      readyState: 4,
      timeout: [Getter/Setter],
      withCredentials: [Getter/Setter],
      upload: [Getter],
      responseURL: 'http://localhost:3000/api/v1/core/organizations/list/',
      status: 500,
      statusText: 'Internal Server Error',
      responseType: [Getter/Setter],
      response: [Getter],
      responseText: [Getter],
      responseXML: [Getter],
      UNSENT: 0,
      OPENED: 1,
      HEADERS_RECEIVED: 2,
      LOADING: 3,
      DONE: 4,
      [Symbol(isMockedResponse)]: true
    }
  }
}
stderr | src/pages/NotFoundPage.test.tsx > NotFoundPage > should render dashboard redirect
Warning: `ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info.
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stdout | src/pages/ProfilePage.test.tsx > ProfilePage > should display user data error
value: null
value: null
stderr | src/pages/ProfilePage.test.tsx > ProfilePage > should display user data error
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/pages/NotFoundPage.test.tsx > NotFoundPage > should render login redirect
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
 ✓ src/pages/NotFoundPage.test.tsx  (2 tests) 1566ms
stdout | src/pages/ProfilePage.test.tsx > ProfilePage > should display user data error
<empty line>
stderr | src/pages/ProfilePage.test.tsx > ProfilePage > should display user data error
AxiosError: Request failed with status code 500
    at settle (file:///app/node_modules/axios/lib/core/settle.js:19:12)
    at XMLHttpRequest.onloadend (file:///app/node_modules/axios/lib/adapters/xhr.js:107:7)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at finalizeResponse (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:329:12)
    at XMLHttpRequestController.respondWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:364:7)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:120:39)
    at runNextTicks (node:internal/process/task_queues:60:5)
    at processTimers (node:internal/timers:516:9) {
  code: 'ERR_BAD_RESPONSE',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/users/update-data/',
    data: '{"email":"<EMAIL>","first_name":"Something","last_name":"Else"}',
    params: {},
    method: 'patch'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function (anonymous)],
    getAllResponseHeaders: [Function (anonymous)],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: 'http://localhost:3000/api/v1/users/update-data/',
    status: 500,
    statusText: 'Internal Server Error',
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4,
    [Symbol(isMockedResponse)]: true
  },
  response: {
    data: '',
    status: 500,
    statusText: 'Internal Server Error',
    headers: AxiosHeaders {},
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [AxiosHeaders],
      url: '/api/v1/users/update-data/',
      data: '{"email":"<EMAIL>","first_name":"Something","last_name":"Else"}',
      params: {},
      method: 'patch'
    },
    request: XMLHttpRequest {
      open: [Function: open],
      setRequestHeader: [Function: setRequestHeader],
      send: [Function: send],
      abort: [Function: abort],
      getResponseHeader: [Function (anonymous)],
      getAllResponseHeaders: [Function (anonymous)],
      overrideMimeType: [Function: overrideMimeType],
      onreadystatechange: [Getter/Setter],
      readyState: 4,
      timeout: [Getter/Setter],
      withCredentials: [Getter/Setter],
      upload: [Getter],
      responseURL: 'http://localhost:3000/api/v1/users/update-data/',
      status: 500,
      statusText: 'Internal Server Error',
      responseType: [Getter/Setter],
      response: [Getter],
      responseText: [Getter],
      responseXML: [Getter],
      UNSENT: 0,
      OPENED: 1,
      HEADERS_RECEIVED: 2,
      LOADING: 3,
      DONE: 4,
      [Symbol(isMockedResponse)]: true
    }
  }
}
stderr | src/pages/ConnectionsPage.test.tsx > ConnectionsPage > should render connection items
Warning: `ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info.
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stdout | src/pages/ConnectionsPage.test.tsx > ConnectionsPage > should render connection items
value: null
value: null
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should display projects error
value: null
value: null
value: null
value: null
value: null
value: null
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should display projects error
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
AxiosError: Request failed with status code 500
    at settle (file:///app/node_modules/axios/lib/core/settle.js:19:12)
    at XMLHttpRequest.onloadend (file:///app/node_modules/axios/lib/adapters/xhr.js:107:7)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at finalizeResponse (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:329:12)
    at XMLHttpRequestController.respondWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:364:7)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:120:39) {
  code: 'ERR_BAD_RESPONSE',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/projects/list/?organization_uuids=organization-uuid-1',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function (anonymous)],
    getAllResponseHeaders: [Function (anonymous)],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: 'http://localhost:3000/api/v1/core/projects/list/?organization_uuids=organization-uuid-1',
    status: 500,
    statusText: 'Internal Server Error',
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4,
    [Symbol(isMockedResponse)]: true
  },
  response: {
    data: '',
    status: 500,
    statusText: 'Internal Server Error',
    headers: AxiosHeaders {},
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [AxiosHeaders],
      url: '/api/v1/core/projects/list/?organization_uuids=organization-uuid-1',
      data: '{}',
      params: {},
      method: 'get'
    },
    request: XMLHttpRequest {
      open: [Function: open],
      setRequestHeader: [Function: setRequestHeader],
      send: [Function: send],
      abort: [Function: abort],
      getResponseHeader: [Function (anonymous)],
      getAllResponseHeaders: [Function (anonymous)],
      overrideMimeType: [Function: overrideMimeType],
      onreadystatechange: [Getter/Setter],
      readyState: 4,
      timeout: [Getter/Setter],
      withCredentials: [Getter/Setter],
      upload: [Getter],
      responseURL: 'http://localhost:3000/api/v1/core/projects/list/?organization_uuids=organization-uuid-1',
      status: 500,
      statusText: 'Internal Server Error',
      responseType: [Getter/Setter],
      response: [Getter],
      responseText: [Getter],
      responseXML: [Getter],
      UNSENT: 0,
      OPENED: 1,
      HEADERS_RECEIVED: 2,
      LOADING: 3,
      DONE: 4,
      [Symbol(isMockedResponse)]: true
    }
  }
}
stdout | src/pages/ProfilePage.test.tsx > ProfilePage > should initiate password reset
value: null
value: null
stderr | src/pages/ProfilePage.test.tsx > ProfilePage > should initiate password reset
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stdout | src/pages/ConnectionsPage.test.tsx > ConnectionsPage > should display connections error
value: null
value: null
stderr | src/pages/ConnectionsPage.test.tsx > ConnectionsPage > should display connections error
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32)
    at runNextTicks (node:internal/process/task_queues:60:5)
    at listOnTimeout (node:internal/timers:545:9)
    at processTimers (node:internal/timers:519:7) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/app/WithAntdConfig.test.tsx > WithAntdConfig > works correctly
Warning: `ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info.
 ✓ src/app/WithAntdConfig.test.tsx  (1 test) 278ms
stdout | src/pages/ConnectionsPage.test.tsx > ConnectionsPage > should display connections error
<empty line>
stderr | src/pages/ConnectionsPage.test.tsx > ConnectionsPage > should display connections error
AxiosError: Request failed with status code 500
    at settle (file:///app/node_modules/axios/lib/core/settle.js:19:12)
    at XMLHttpRequest.onloadend (file:///app/node_modules/axios/lib/adapters/xhr.js:107:7)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at finalizeResponse (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:329:12)
    at XMLHttpRequestController.respondWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:364:7)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:120:39) {
  code: 'ERR_BAD_RESPONSE',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/organizations/retrieve-connections/organization-uuid-1/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function (anonymous)],
    getAllResponseHeaders: [Function (anonymous)],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: 'http://localhost:3000/api/v1/core/organizations/retrieve-connections/organization-uuid-1/',
    status: 500,
    statusText: 'Internal Server Error',
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4,
    [Symbol(isMockedResponse)]: true
  },
  response: {
    data: '',
    status: 500,
    statusText: 'Internal Server Error',
    headers: AxiosHeaders {},
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [AxiosHeaders],
      url: '/api/v1/core/organizations/retrieve-connections/organization-uuid-1/',
      data: '{}',
      params: {},
      method: 'get'
    },
    request: XMLHttpRequest {
      open: [Function: open],
      setRequestHeader: [Function: setRequestHeader],
      send: [Function: send],
      abort: [Function: abort],
      getResponseHeader: [Function (anonymous)],
      getAllResponseHeaders: [Function (anonymous)],
      overrideMimeType: [Function: overrideMimeType],
      onreadystatechange: [Getter/Setter],
      readyState: 4,
      timeout: [Getter/Setter],
      withCredentials: [Getter/Setter],
      upload: [Getter],
      responseURL: 'http://localhost:3000/api/v1/core/organizations/retrieve-connections/organization-uuid-1/',
      status: 500,
      statusText: 'Internal Server Error',
      responseType: [Getter/Setter],
      response: [Getter],
      responseText: [Getter],
      responseXML: [Getter],
      UNSENT: 0,
      OPENED: 1,
      HEADERS_RECEIVED: 2,
      LOADING: 3,
      DONE: 4,
      [Symbol(isMockedResponse)]: true
    }
  }
}
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should display flows error
value: null
value: null
value: null
value: null
value: null
value: null
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should display flows error
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
AxiosError: Request failed with status code 500
    at settle (file:///app/node_modules/axios/lib/core/settle.js:19:12)
    at XMLHttpRequest.onloadend (file:///app/node_modules/axios/lib/adapters/xhr.js:107:7)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at finalizeResponse (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:329:12)
    at XMLHttpRequestController.respondWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:364:7)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:120:39) {
  code: 'ERR_BAD_RESPONSE',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/flows/list/?project_uuids=project-uuid-1',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function (anonymous)],
    getAllResponseHeaders: [Function (anonymous)],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: 'http://localhost:3000/api/v1/flows/list/?project_uuids=project-uuid-1',
    status: 500,
    statusText: 'Internal Server Error',
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4,
    [Symbol(isMockedResponse)]: true
  },
  response: {
    data: '',
    status: 500,
    statusText: 'Internal Server Error',
    headers: AxiosHeaders {},
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [AxiosHeaders],
      url: '/api/v1/flows/list/?project_uuids=project-uuid-1',
      data: '{}',
      params: {},
      method: 'get'
    },
    request: XMLHttpRequest {
      open: [Function: open],
      setRequestHeader: [Function: setRequestHeader],
      send: [Function: send],
      abort: [Function: abort],
      getResponseHeader: [Function (anonymous)],
      getAllResponseHeaders: [Function (anonymous)],
      overrideMimeType: [Function: overrideMimeType],
      onreadystatechange: [Getter/Setter],
      readyState: 4,
      timeout: [Getter/Setter],
      withCredentials: [Getter/Setter],
      upload: [Getter],
      responseURL: 'http://localhost:3000/api/v1/flows/list/?project_uuids=project-uuid-1',
      status: 500,
      statusText: 'Internal Server Error',
      responseType: [Getter/Setter],
      response: [Getter],
      responseText: [Getter],
      responseXML: [Getter],
      UNSENT: 0,
      OPENED: 1,
      HEADERS_RECEIVED: 2,
      LOADING: 3,
      DONE: 4,
      [Symbol(isMockedResponse)]: true
    }
  }
}
stdout | src/pages/ProfilePage.test.tsx > ProfilePage > should display password reset error
value: null
value: null
stderr | src/pages/ProfilePage.test.tsx > ProfilePage > should display password reset error
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/pages/IndexPage.test.tsx > IndexPage > should redirect to login
Warning: `ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info.
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stdout | src/pages/ProfilePage.test.tsx > ProfilePage > should display password reset error
<empty line>
stderr | src/pages/ProfilePage.test.tsx > ProfilePage > should display password reset error
AxiosError: Request failed with status code 500
    at settle (file:///app/node_modules/axios/lib/core/settle.js:19:12)
    at XMLHttpRequest.onloadend (file:///app/node_modules/axios/lib/adapters/xhr.js:107:7)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at finalizeResponse (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:329:12)
    at XMLHttpRequestController.respondWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:364:7)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:120:39)
    at runNextTicks (node:internal/process/task_queues:60:5)
    at processTimers (node:internal/timers:516:9) {
  code: 'ERR_BAD_RESPONSE',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/users/change-password/',
    data: '{}',
    params: {},
    method: 'post'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function (anonymous)],
    getAllResponseHeaders: [Function (anonymous)],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: 'http://localhost:3000/api/v1/users/change-password/',
    status: 500,
    statusText: 'Internal Server Error',
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4,
    [Symbol(isMockedResponse)]: true
  },
  response: {
    data: '',
    status: 500,
    statusText: 'Internal Server Error',
    headers: AxiosHeaders {},
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [AxiosHeaders],
      url: '/api/v1/users/change-password/',
      data: '{}',
      params: {},
      method: 'post'
    },
    request: XMLHttpRequest {
      open: [Function: open],
      setRequestHeader: [Function: setRequestHeader],
      send: [Function: send],
      abort: [Function: abort],
      getResponseHeader: [Function (anonymous)],
      getAllResponseHeaders: [Function (anonymous)],
      overrideMimeType: [Function: overrideMimeType],
      onreadystatechange: [Getter/Setter],
      readyState: 4,
      timeout: [Getter/Setter],
      withCredentials: [Getter/Setter],
      upload: [Getter],
      responseURL: 'http://localhost:3000/api/v1/users/change-password/',
      status: 500,
      statusText: 'Internal Server Error',
      responseType: [Getter/Setter],
      response: [Getter],
      responseText: [Getter],
      responseXML: [Getter],
      UNSENT: 0,
      OPENED: 1,
      HEADERS_RECEIVED: 2,
      LOADING: 3,
      DONE: 4,
      [Symbol(isMockedResponse)]: true
    }
  }
}
 ✓ src/pages/ConnectionsPage.test.tsx  (2 tests) 4429ms
stderr | src/pages/IndexPage.test.tsx > IndexPage > should redirect to dashboard
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should switch language
value: null
value: null
value: null
value: null
value: null
value: null
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should switch language
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stdout | src/pages/ProfilePage.test.tsx > ProfilePage > should display organizations
value: null
value: null
stderr | src/pages/ProfilePage.test.tsx > ProfilePage > should display organizations
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/organizations/profile-list/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
Failed to fetch organizations with role: AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32)
    at runNextTicks (node:internal/process/task_queues:60:5)
    at listOnTimeout (node:internal/timers:545:9)
    at processTimers (node:internal/timers:519:7) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/organizations/profile-list/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should switch language
<empty line>
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should switch language
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/IndexPage.test.tsx > IndexPage > should redirect to dashboard
<empty line>
stdout | src/pages/IndexPage.test.tsx > IndexPage > should redirect to dashboard
value: null
value: null
value: null
value: null
value: null
value: null
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
 ✓ src/pages/IndexPage.test.tsx  (2 tests) 2941ms
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should switch language
<empty line>
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should switch language
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should navigate to profile
value: null
value: null
value: null
value: null
value: null
value: null
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should navigate to profile
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should log out
value: null
value: null
value: null
value: null
value: null
value: null
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should log out
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should display logout error
value: null
value: null
value: null
value: null
value: null
value: null
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should display logout error
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32)
    at runNextTicks (node:internal/process/task_queues:60:5)
    at processTimers (node:internal/timers:516:9) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should display logout error
<empty line>
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should display logout error
AxiosError: Request failed with status code 500
    at settle (file:///app/node_modules/axios/lib/core/settle.js:19:12)
    at XMLHttpRequest.onloadend (file:///app/node_modules/axios/lib/adapters/xhr.js:107:7)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at finalizeResponse (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:329:12)
    at XMLHttpRequestController.respondWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:364:7)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:120:39) {
  code: 'ERR_BAD_RESPONSE',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/users/logout/',
    data: '{}',
    params: {},
    method: 'post'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function (anonymous)],
    getAllResponseHeaders: [Function (anonymous)],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: 'http://localhost:3000/api/v1/users/logout/',
    status: 500,
    statusText: 'Internal Server Error',
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4,
    [Symbol(isMockedResponse)]: true
  },
  response: {
    data: '',
    status: 500,
    statusText: 'Internal Server Error',
    headers: AxiosHeaders {},
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [AxiosHeaders],
      url: '/api/v1/users/logout/',
      data: '{}',
      params: {},
      method: 'post'
    },
    request: XMLHttpRequest {
      open: [Function: open],
      setRequestHeader: [Function: setRequestHeader],
      send: [Function: send],
      abort: [Function: abort],
      getResponseHeader: [Function (anonymous)],
      getAllResponseHeaders: [Function (anonymous)],
      overrideMimeType: [Function: overrideMimeType],
      onreadystatechange: [Getter/Setter],
      readyState: 4,
      timeout: [Getter/Setter],
      withCredentials: [Getter/Setter],
      upload: [Getter],
      responseURL: 'http://localhost:3000/api/v1/users/logout/',
      status: 500,
      statusText: 'Internal Server Error',
      responseType: [Getter/Setter],
      response: [Getter],
      responseText: [Getter],
      responseXML: [Getter],
      UNSENT: 0,
      OPENED: 1,
      HEADERS_RECEIVED: 2,
      LOADING: 3,
      DONE: 4,
      [Symbol(isMockedResponse)]: true
    }
  }
}
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should collapse sidebar
value: null
value: null
value: null
value: null
value: null
value: null
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
newValue: false
JSON.stringify(newValue): false
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should collapse sidebar
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should collapse sidebar
newValue: true
JSON.stringify(newValue): true
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should collapse sidebar
<empty line>
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should render flow calls table
value: null
value: null
value: null
value: null
value: null
value: null
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should render flow calls table
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32)
    at runNextTicks (node:internal/process/task_queues:60:5)
    at processTimers (node:internal/timers:516:9) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should display flow calls error
value: null
value: null
value: null
value: null
value: null
value: null
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should display flow calls error
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
AxiosError: Request failed with status code 500
    at settle (file:///app/node_modules/axios/lib/core/settle.js:19:12)
    at XMLHttpRequest.onloadend (file:///app/node_modules/axios/lib/adapters/xhr.js:107:7)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at finalizeResponse (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:329:12)
    at XMLHttpRequestController.respondWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:364:7)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:120:39) {
  code: 'ERR_BAD_RESPONSE',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/analytics/projects/logs/?bulk_result=True&exclude_nodes=True&project_uuids=project-uuid-1&limit=100&offset=0&',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function (anonymous)],
    getAllResponseHeaders: [Function (anonymous)],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: 'http://localhost:3000/api/v1/analytics/projects/logs/?bulk_result=True&exclude_nodes=True&project_uuids=project-uuid-1&limit=100&offset=0&',
    status: 500,
    statusText: 'Internal Server Error',
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4,
    [Symbol(isMockedResponse)]: true
  },
  response: {
    data: '',
    status: 500,
    statusText: 'Internal Server Error',
    headers: AxiosHeaders {},
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [AxiosHeaders],
      url: '/api/v1/analytics/projects/logs/?bulk_result=True&exclude_nodes=True&project_uuids=project-uuid-1&limit=100&offset=0&',
      data: '{}',
      params: {},
      method: 'get'
    },
    request: XMLHttpRequest {
      open: [Function: open],
      setRequestHeader: [Function: setRequestHeader],
      send: [Function: send],
      abort: [Function: abort],
      getResponseHeader: [Function (anonymous)],
      getAllResponseHeaders: [Function (anonymous)],
      overrideMimeType: [Function: overrideMimeType],
      onreadystatechange: [Getter/Setter],
      readyState: 4,
      timeout: [Getter/Setter],
      withCredentials: [Getter/Setter],
      upload: [Getter],
      responseURL: 'http://localhost:3000/api/v1/analytics/projects/logs/?bulk_result=True&exclude_nodes=True&project_uuids=project-uuid-1&limit=100&offset=0&',
      status: 500,
      statusText: 'Internal Server Error',
      responseType: [Getter/Setter],
      response: [Getter],
      responseText: [Getter],
      responseXML: [Getter],
      UNSENT: 0,
      OPENED: 1,
      HEADERS_RECEIVED: 2,
      LOADING: 3,
      DONE: 4,
      [Symbol(isMockedResponse)]: true
    }
  }
}
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should display flow schema
value: null
value: null
value: null
value: null
value: null
value: null
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should display flow schema
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stdout | src/pages/ProfilePage.test.tsx > ProfilePage > should leave organization
value: null
value: null
stderr | src/pages/ProfilePage.test.tsx > ProfilePage > should leave organization
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/organizations/profile-list/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
Failed to fetch organizations with role: AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32)
    at runNextTicks (node:internal/process/task_queues:60:5)
    at listOnTimeout (node:internal/timers:545:9)
    at processTimers (node:internal/timers:519:7) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/organizations/profile-list/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should display flow schema
<empty line>
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should display flow schema
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should display flow schema
<empty line>
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should display flow schema
newValue: true
JSON.stringify(newValue): true
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should display flow schema error
value: null
value: null
value: null
value: null
value: null
value: null
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should display flow schema error
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should display flow schema error
<empty line>
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should display flow schema error
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stdout | src/pages/DashboardPage.test.tsx > DashboardPage > should display flow schema error
newValue: true
JSON.stringify(newValue): true
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
dashboardConfiguration: { refreshValue: 30000 }
refreshValue: 30000
stderr | src/pages/DashboardPage.test.tsx > DashboardPage > should display flow schema error
AxiosError: Request failed with status code 500
    at settle (file:///app/node_modules/axios/lib/core/settle.js:19:12)
    at XMLHttpRequest.onloadend (file:///app/node_modules/axios/lib/adapters/xhr.js:107:7)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at finalizeResponse (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:329:12)
    at XMLHttpRequestController.respondWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:364:7)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:120:39)
    at runNextTicks (node:internal/process/task_queues:60:5)
    at listOnTimeout (node:internal/timers:545:9) {
  code: 'ERR_BAD_RESPONSE',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/flows/retrieve/flow-uuid-1/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function (anonymous)],
    getAllResponseHeaders: [Function (anonymous)],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: 'http://localhost:3000/api/v1/flows/retrieve/flow-uuid-1/',
    status: 500,
    statusText: 'Internal Server Error',
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4,
    [Symbol(isMockedResponse)]: true
  },
  response: {
    data: '',
    status: 500,
    statusText: 'Internal Server Error',
    headers: AxiosHeaders {},
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [AxiosHeaders],
      url: '/api/v1/flows/retrieve/flow-uuid-1/',
      data: '{}',
      params: {},
      method: 'get'
    },
    request: XMLHttpRequest {
      open: [Function: open],
      setRequestHeader: [Function: setRequestHeader],
      send: [Function: send],
      abort: [Function: abort],
      getResponseHeader: [Function (anonymous)],
      getAllResponseHeaders: [Function (anonymous)],
      overrideMimeType: [Function: overrideMimeType],
      onreadystatechange: [Getter/Setter],
      readyState: 4,
      timeout: [Getter/Setter],
      withCredentials: [Getter/Setter],
      upload: [Getter],
      responseURL: 'http://localhost:3000/api/v1/flows/retrieve/flow-uuid-1/',
      status: 500,
      statusText: 'Internal Server Error',
      responseType: [Getter/Setter],
      response: [Getter],
      responseText: [Getter],
      responseXML: [Getter],
      UNSENT: 0,
      OPENED: 1,
      HEADERS_RECEIVED: 2,
      LOADING: 3,
      DONE: 4,
      [Symbol(isMockedResponse)]: true
    }
  }
}
 ✓ src/pages/DashboardPage.test.tsx  (16 tests) 34309ms
stdout | src/pages/ProfilePage.test.tsx > ProfilePage > should display leave organization error
value: null
value: null
stderr | src/pages/ProfilePage.test.tsx > ProfilePage > should display leave organization error
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/organizations/profile-list/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
Failed to fetch organizations with role: AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32)
    at runNextTicks (node:internal/process/task_queues:60:5)
    at listOnTimeout (node:internal/timers:545:9)
    at processTimers (node:internal/timers:519:7) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/organizations/profile-list/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
stdout | src/pages/ProfilePage.test.tsx > ProfilePage > should render export tab
value: null
value: null
stderr | src/pages/ProfilePage.test.tsx > ProfilePage > should render export tab
[MSW] Error: intercepted a request without a matching request handler:
  • GET /api/v1/core/app-info/
If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/getting-started/mocks
AxiosError: Network Error
    at XMLHttpRequest.handleError (file:///app/node_modules/axios/lib/adapters/xhr.js:158:14)
    at XMLHttpRequest.methodCall (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:194:20)
    at Proxy.<anonymous> (file:///app/node_modules/@mswjs/interceptors/src/utils/createProxy.ts:93:29)
    at XMLHttpRequestController.trigger (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:530:16)
    at XMLHttpRequestController.errorWith (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts:484:10)
    at XMLHttpRequestController.xhrRequestController.onRequest (file:///app/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts:98:32) {
  code: 'ERR_NETWORK',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: AxiosHeaders {
      Accept: 'application/json; charset=UTF-8',
      'Content-Type': 'application/json'
    },
    url: '/api/v1/core/app-info/',
    data: '{}',
    params: {},
    method: 'get'
  },
  request: XMLHttpRequest {
    open: [Function: open],
    setRequestHeader: [Function: setRequestHeader],
    send: [Function: send],
    abort: [Function: abort],
    getResponseHeader: [Function: getResponseHeader],
    getAllResponseHeaders: [Function: getAllResponseHeaders],
    overrideMimeType: [Function: overrideMimeType],
    onreadystatechange: [Getter/Setter],
    readyState: 4,
    timeout: [Getter/Setter],
    withCredentials: [Getter/Setter],
    upload: [Getter],
    responseURL: [Getter],
    status: [Getter],
    statusText: [Getter],
    responseType: [Getter/Setter],
    response: [Getter],
    responseText: [Getter],
    responseXML: [Getter],
    UNSENT: 0,
    OPENED: 1,
    HEADERS_RECEIVED: 2,
    LOADING: 3,
    DONE: 4
  }
}
 ❯ src/pages/ProfilePage.test.tsx  (12 tests | 3 failed) 53890ms
   ❯ src/pages/ProfilePage.test.tsx > ProfilePage > should display organizations
     → Unable to find an element by: [data-testid="organization-item"]
Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="css-dev-only-do-not-override-1f507pn ant-app"
    >
      <div
        data-testid="user-loaded"
      />
      <div
        class="ant-layout css-dev-only-do-not-override-1f507pn"
        style="min-height: 100vh;"
      >
        <div
          class="_container_2fb522"
        >
          <div
            class="_leftSide_2fb522"
          >
            <svg
              class="_logo_2fb522"
              id="Layer_1"
              style="enable-background: new 0 0 422.5 77.4;"
              viewBox="0 0 422.5 77.4"
              x="0px"
              xml:space="preserve"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              y="0px"
            >
              <g>
                <path
                  d="M58.2,52.4c0,14.5-11.4,24.3-28.4,24.3C13.4,76.6,2.4,67.8,0,52.8l17.7-2.2c1.6,6,5.4,8.7,12.3,8.7c9.9,0,9.9-4.4,9.9-6.5 c0-3.2,0-4.1-12.6-7.1C14.4,42.6,2.5,37.7,2.5,23c0-11.2,8.2-23.8,26.7-23.8c18.5,0,26.5,12.3,27.6,22.7l-17.7,1.9 c-0.9-4.7-4.4-7.3-10.3-7.3c-8.4,0-8.4,4.6-8.4,5.8c0,1.7,0,3.5,11,6.2C50.6,33.1,58.2,39,58.2,52.4z"
                />
                <path
                  d="M330.8,2.5L330.8,2.5c-0.1-0.1-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0c-1.3-0.1-2.6-0.2-4-0.2 c0,0,0,0,0,0s0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10c-1.8-0.3-3.6-0.5-5.5-0.5 c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3c-0.1,0-0.1,0.1-0.2,0.1l0,0 c-3.6,1.8-6.8,4.2-9.5,7.2c-2.7-3-5.9-5.5-9.5-7.2l0,0c-0.1,0-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0 c-1.3-0.1-2.6-0.2-4-0.2c0,0,0,0,0,0c0,0,0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10 c-1.8-0.3-3.6-0.5-5.5-0.5c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3 c-0.1,0-0.1,0.1-0.2,0.1l0,0C144,8.3,136.8,21.1,136.8,38v27.2v9.2h18.9V38c0-1.5,0.1-3.5,0.4-5.6c1-6.4,4.3-14.2,14.4-14.2 c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2 c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4,0.4,5.5c0,0,0,0.1,0,0.1v27.2v9.2h0h18.9h0v-2.6V38c0,0,0-0.1,0-0.1 c0-1.5,0.1-3.4,0.4-5.5c1-6.4,4.3-14.2,14.4-14.2c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38 c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4.1,0.4,5.6v36.4h18.9v-2.6V38 C349.7,21.1,342.5,8.3,330.8,2.5z"
                />
                <g>
                  <path
                    d="M112.5,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H64v13.9c0,23.2,13.6,38.8,33.8,38.8c0,0,0,0,0,0 c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H112.5z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="88.2"
                    y="47.9"
                  />
                </g>
                <g>
                  <path
                    d="M403.6,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H355v13.9c0,23.2,13.6,38.8,33.8,38.8 c0,0,0,0,0,0c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H403.6z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="379.3"
                    y="47.9"
                  />
                </g>
              </g>
            </svg>
            <div
              class="ant-select ant-select-outlined _select_270c7e css-dev-only-do-not-override-1f507pn ant-select-single ant-select-show-arrow ant-select-show-search"
              data-testid="organization-select"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-search"
                >
                  <input
                    aria-autocomplete="list"
                    aria-controls="rc_select_TEST_OR_SSR_list"
                    aria-expanded="false"
                    aria-haspopup="listbox"
                    aria-owns="rc_select_TEST_OR_SSR_list"
                    autocomplete="off"
                    class="ant-select-selection-search-input"
                    id="rc_select_TEST_OR_SSR"
                    role="combobox"
                    type="search"
                    value=""
                  />
                </span>
                <span
                  class="ant-select-selection-item"
                  title="Organization 1"
                >
                  Organization 1
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select: none;"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width=
Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="css-dev-only-do-not-override-1f507pn ant-app"
    >
      <div
        data-testid="user-loaded"
      />
      <div
        class="ant-layout css-dev-only-do-not-override-1f507pn"
        style="min-height: 100vh;"
      >
        <div
          class="_container_2fb522"
        >
          <div
            class="_leftSide_2fb522"
          >
            <svg
              class="_logo_2fb522"
              id="Layer_1"
              style="enable-background: new 0 0 422.5 77.4;"
              viewBox="0 0 422.5 77.4"
              x="0px"
              xml:space="preserve"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              y="0px"
            >
              <g>
                <path
                  d="M58.2,52.4c0,14.5-11.4,24.3-28.4,24.3C13.4,76.6,2.4,67.8,0,52.8l17.7-2.2c1.6,6,5.4,8.7,12.3,8.7c9.9,0,9.9-4.4,9.9-6.5 c0-3.2,0-4.1-12.6-7.1C14.4,42.6,2.5,37.7,2.5,23c0-11.2,8.2-23.8,26.7-23.8c18.5,0,26.5,12.3,27.6,22.7l-17.7,1.9 c-0.9-4.7-4.4-7.3-10.3-7.3c-8.4,0-8.4,4.6-8.4,5.8c0,1.7,0,3.5,11,6.2C50.6,33.1,58.2,39,58.2,52.4z"
                />
                <path
                  d="M330.8,2.5L330.8,2.5c-0.1-0.1-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0c-1.3-0.1-2.6-0.2-4-0.2 c0,0,0,0,0,0s0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10c-1.8-0.3-3.6-0.5-5.5-0.5 c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3c-0.1,0-0.1,0.1-0.2,0.1l0,0 c-3.6,1.8-6.8,4.2-9.5,7.2c-2.7-3-5.9-5.5-9.5-7.2l0,0c-0.1,0-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0 c-1.3-0.1-2.6-0.2-4-0.2c0,0,0,0,0,0c0,0,0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10 c-1.8-0.3-3.6-0.5-5.5-0.5c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3 c-0.1,0-0.1,0.1-0.2,0.1l0,0C144,8.3,136.8,21.1,136.8,38v27.2v9.2h18.9V38c0-1.5,0.1-3.5,0.4-5.6c1-6.4,4.3-14.2,14.4-14.2 c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2 c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4,0.4,5.5c0,0,0,0.1,0,0.1v27.2v9.2h0h18.9h0v-2.6V38c0,0,0-0.1,0-0.1 c0-1.5,0.1-3.4,0.4-5.5c1-6.4,4.3-14.2,14.4-14.2c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38 c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4.1,0.4,5.6v36.4h18.9v-2.6V38 C349.7,21.1,342.5,8.3,330.8,2.5z"
                />
                <g>
                  <path
                    d="M112.5,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H64v13.9c0,23.2,13.6,38.8,33.8,38.8c0,0,0,0,0,0 c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H112.5z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="88.2"
                    y="47.9"
                  />
                </g>
                <g>
                  <path
                    d="M403.6,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H355v13.9c0,23.2,13.6,38.8,33.8,38.8 c0,0,0,0,0,0c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H403.6z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="379.3"
                    y="47.9"
                  />
                </g>
              </g>
            </svg>
            <div
              class="ant-select ant-select-outlined _select_270c7e css-dev-only-do-not-override-1f507pn ant-select-single ant-select-show-arrow ant-select-show-search"
              data-testid="organization-select"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-search"
                >
                  <input
                    aria-autocomplete="list"
                    aria-controls="rc_select_TEST_OR_SSR_list"
                    aria-expanded="false"
                    aria-haspopup="listbox"
                    aria-owns="rc_select_TEST_OR_SSR_list"
                    autocomplete="off"
                    class="ant-select-selection-search-input"
                    id="rc_select_TEST_OR_SSR"
                    role="combobox"
                    type="search"
                    value=""
                  />
                </span>
                <span
                  class="ant-select-selection-item"
                  title="Organization 1"
                >
                  Organization 1
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select: none;"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width=   ❯ src/pages/ProfilePage.test.tsx > ProfilePage > should leave organization
     → Unable to find an element by: [data-testid="leave-organization-button"]
Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="css-dev-only-do-not-override-1f507pn ant-app"
    >
      <div
        data-testid="user-loaded"
      />
      <div
        class="ant-layout css-dev-only-do-not-override-1f507pn"
        style="min-height: 100vh;"
      >
        <div
          class="_container_2fb522"
        >
          <div
            class="_leftSide_2fb522"
          >
            <svg
              class="_logo_2fb522"
              id="Layer_1"
              style="enable-background: new 0 0 422.5 77.4;"
              viewBox="0 0 422.5 77.4"
              x="0px"
              xml:space="preserve"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              y="0px"
            >
              <g>
                <path
                  d="M58.2,52.4c0,14.5-11.4,24.3-28.4,24.3C13.4,76.6,2.4,67.8,0,52.8l17.7-2.2c1.6,6,5.4,8.7,12.3,8.7c9.9,0,9.9-4.4,9.9-6.5 c0-3.2,0-4.1-12.6-7.1C14.4,42.6,2.5,37.7,2.5,23c0-11.2,8.2-23.8,26.7-23.8c18.5,0,26.5,12.3,27.6,22.7l-17.7,1.9 c-0.9-4.7-4.4-7.3-10.3-7.3c-8.4,0-8.4,4.6-8.4,5.8c0,1.7,0,3.5,11,6.2C50.6,33.1,58.2,39,58.2,52.4z"
                />
                <path
                  d="M330.8,2.5L330.8,2.5c-0.1-0.1-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0c-1.3-0.1-2.6-0.2-4-0.2 c0,0,0,0,0,0s0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10c-1.8-0.3-3.6-0.5-5.5-0.5 c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3c-0.1,0-0.1,0.1-0.2,0.1l0,0 c-3.6,1.8-6.8,4.2-9.5,7.2c-2.7-3-5.9-5.5-9.5-7.2l0,0c-0.1,0-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0 c-1.3-0.1-2.6-0.2-4-0.2c0,0,0,0,0,0c0,0,0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10 c-1.8-0.3-3.6-0.5-5.5-0.5c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3 c-0.1,0-0.1,0.1-0.2,0.1l0,0C144,8.3,136.8,21.1,136.8,38v27.2v9.2h18.9V38c0-1.5,0.1-3.5,0.4-5.6c1-6.4,4.3-14.2,14.4-14.2 c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2 c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4,0.4,5.5c0,0,0,0.1,0,0.1v27.2v9.2h0h18.9h0v-2.6V38c0,0,0-0.1,0-0.1 c0-1.5,0.1-3.4,0.4-5.5c1-6.4,4.3-14.2,14.4-14.2c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38 c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4.1,0.4,5.6v36.4h18.9v-2.6V38 C349.7,21.1,342.5,8.3,330.8,2.5z"
                />
                <g>
                  <path
                    d="M112.5,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H64v13.9c0,23.2,13.6,38.8,33.8,38.8c0,0,0,0,0,0 c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H112.5z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="88.2"
                    y="47.9"
                  />
                </g>
                <g>
                  <path
                    d="M403.6,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H355v13.9c0,23.2,13.6,38.8,33.8,38.8 c0,0,0,0,0,0c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H403.6z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="379.3"
                    y="47.9"
                  />
                </g>
              </g>
            </svg>
            <div
              class="ant-select ant-select-outlined _select_270c7e css-dev-only-do-not-override-1f507pn ant-select-single ant-select-show-arrow ant-select-show-search"
              data-testid="organization-select"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-search"
                >
                  <input
                    aria-autocomplete="list"
                    aria-controls="rc_select_TEST_OR_SSR_list"
                    aria-expanded="false"
                    aria-haspopup="listbox"
                    aria-owns="rc_select_TEST_OR_SSR_list"
                    autocomplete="off"
                    class="ant-select-selection-search-input"
                    id="rc_select_TEST_OR_SSR"
                    role="combobox"
                    type="search"
                    value=""
                  />
                </span>
                <span
                  class="ant-select-selection-item"
                  title="Organization 1"
                >
                  Organization 1
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select: none;"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width=
Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="css-dev-only-do-not-override-1f507pn ant-app"
    >
      <div
        data-testid="user-loaded"
      />
      <div
        class="ant-layout css-dev-only-do-not-override-1f507pn"
        style="min-height: 100vh;"
      >
        <div
          class="_container_2fb522"
        >
          <div
            class="_leftSide_2fb522"
          >
            <svg
              class="_logo_2fb522"
              id="Layer_1"
              style="enable-background: new 0 0 422.5 77.4;"
              viewBox="0 0 422.5 77.4"
              x="0px"
              xml:space="preserve"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              y="0px"
            >
              <g>
                <path
                  d="M58.2,52.4c0,14.5-11.4,24.3-28.4,24.3C13.4,76.6,2.4,67.8,0,52.8l17.7-2.2c1.6,6,5.4,8.7,12.3,8.7c9.9,0,9.9-4.4,9.9-6.5 c0-3.2,0-4.1-12.6-7.1C14.4,42.6,2.5,37.7,2.5,23c0-11.2,8.2-23.8,26.7-23.8c18.5,0,26.5,12.3,27.6,22.7l-17.7,1.9 c-0.9-4.7-4.4-7.3-10.3-7.3c-8.4,0-8.4,4.6-8.4,5.8c0,1.7,0,3.5,11,6.2C50.6,33.1,58.2,39,58.2,52.4z"
                />
                <path
                  d="M330.8,2.5L330.8,2.5c-0.1-0.1-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0c-1.3-0.1-2.6-0.2-4-0.2 c0,0,0,0,0,0s0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10c-1.8-0.3-3.6-0.5-5.5-0.5 c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3c-0.1,0-0.1,0.1-0.2,0.1l0,0 c-3.6,1.8-6.8,4.2-9.5,7.2c-2.7-3-5.9-5.5-9.5-7.2l0,0c-0.1,0-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0 c-1.3-0.1-2.6-0.2-4-0.2c0,0,0,0,0,0c0,0,0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10 c-1.8-0.3-3.6-0.5-5.5-0.5c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3 c-0.1,0-0.1,0.1-0.2,0.1l0,0C144,8.3,136.8,21.1,136.8,38v27.2v9.2h18.9V38c0-1.5,0.1-3.5,0.4-5.6c1-6.4,4.3-14.2,14.4-14.2 c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2 c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4,0.4,5.5c0,0,0,0.1,0,0.1v27.2v9.2h0h18.9h0v-2.6V38c0,0,0-0.1,0-0.1 c0-1.5,0.1-3.4,0.4-5.5c1-6.4,4.3-14.2,14.4-14.2c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38 c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4.1,0.4,5.6v36.4h18.9v-2.6V38 C349.7,21.1,342.5,8.3,330.8,2.5z"
                />
                <g>
                  <path
                    d="M112.5,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H64v13.9c0,23.2,13.6,38.8,33.8,38.8c0,0,0,0,0,0 c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H112.5z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="88.2"
                    y="47.9"
                  />
                </g>
                <g>
                  <path
                    d="M403.6,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H355v13.9c0,23.2,13.6,38.8,33.8,38.8 c0,0,0,0,0,0c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H403.6z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="379.3"
                    y="47.9"
                  />
                </g>
              </g>
            </svg>
            <div
              class="ant-select ant-select-outlined _select_270c7e css-dev-only-do-not-override-1f507pn ant-select-single ant-select-show-arrow ant-select-show-search"
              data-testid="organization-select"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-search"
                >
                  <input
                    aria-autocomplete="list"
                    aria-controls="rc_select_TEST_OR_SSR_list"
                    aria-expanded="false"
                    aria-haspopup="listbox"
                    aria-owns="rc_select_TEST_OR_SSR_list"
                    autocomplete="off"
                    class="ant-select-selection-search-input"
                    id="rc_select_TEST_OR_SSR"
                    role="combobox"
                    type="search"
                    value=""
                  />
                </span>
                <span
                  class="ant-select-selection-item"
                  title="Organization 1"
                >
                  Organization 1
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select: none;"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width=   ❯ src/pages/ProfilePage.test.tsx > ProfilePage > should display leave organization error
     → Unable to find an element by: [data-testid="leave-organization-button"]
Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="css-dev-only-do-not-override-1f507pn ant-app"
    >
      <div
        data-testid="user-loaded"
      />
      <div
        class="ant-layout css-dev-only-do-not-override-1f507pn"
        style="min-height: 100vh;"
      >
        <div
          class="_container_2fb522"
        >
          <div
            class="_leftSide_2fb522"
          >
            <svg
              class="_logo_2fb522"
              id="Layer_1"
              style="enable-background: new 0 0 422.5 77.4;"
              viewBox="0 0 422.5 77.4"
              x="0px"
              xml:space="preserve"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              y="0px"
            >
              <g>
                <path
                  d="M58.2,52.4c0,14.5-11.4,24.3-28.4,24.3C13.4,76.6,2.4,67.8,0,52.8l17.7-2.2c1.6,6,5.4,8.7,12.3,8.7c9.9,0,9.9-4.4,9.9-6.5 c0-3.2,0-4.1-12.6-7.1C14.4,42.6,2.5,37.7,2.5,23c0-11.2,8.2-23.8,26.7-23.8c18.5,0,26.5,12.3,27.6,22.7l-17.7,1.9 c-0.9-4.7-4.4-7.3-10.3-7.3c-8.4,0-8.4,4.6-8.4,5.8c0,1.7,0,3.5,11,6.2C50.6,33.1,58.2,39,58.2,52.4z"
                />
                <path
                  d="M330.8,2.5L330.8,2.5c-0.1-0.1-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0c-1.3-0.1-2.6-0.2-4-0.2 c0,0,0,0,0,0s0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10c-1.8-0.3-3.6-0.5-5.5-0.5 c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3c-0.1,0-0.1,0.1-0.2,0.1l0,0 c-3.6,1.8-6.8,4.2-9.5,7.2c-2.7-3-5.9-5.5-9.5-7.2l0,0c-0.1,0-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0 c-1.3-0.1-2.6-0.2-4-0.2c0,0,0,0,0,0c0,0,0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10 c-1.8-0.3-3.6-0.5-5.5-0.5c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3 c-0.1,0-0.1,0.1-0.2,0.1l0,0C144,8.3,136.8,21.1,136.8,38v27.2v9.2h18.9V38c0-1.5,0.1-3.5,0.4-5.6c1-6.4,4.3-14.2,14.4-14.2 c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2 c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4,0.4,5.5c0,0,0,0.1,0,0.1v27.2v9.2h0h18.9h0v-2.6V38c0,0,0-0.1,0-0.1 c0-1.5,0.1-3.4,0.4-5.5c1-6.4,4.3-14.2,14.4-14.2c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38 c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4.1,0.4,5.6v36.4h18.9v-2.6V38 C349.7,21.1,342.5,8.3,330.8,2.5z"
                />
                <g>
                  <path
                    d="M112.5,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H64v13.9c0,23.2,13.6,38.8,33.8,38.8c0,0,0,0,0,0 c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H112.5z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="88.2"
                    y="47.9"
                  />
                </g>
                <g>
                  <path
                    d="M403.6,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H355v13.9c0,23.2,13.6,38.8,33.8,38.8 c0,0,0,0,0,0c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H403.6z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="379.3"
                    y="47.9"
                  />
                </g>
              </g>
            </svg>
            <div
              class="ant-select ant-select-outlined _select_270c7e css-dev-only-do-not-override-1f507pn ant-select-single ant-select-show-arrow ant-select-show-search"
              data-testid="organization-select"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-search"
                >
                  <input
                    aria-autocomplete="list"
                    aria-controls="rc_select_TEST_OR_SSR_list"
                    aria-expanded="false"
                    aria-haspopup="listbox"
                    aria-owns="rc_select_TEST_OR_SSR_list"
                    autocomplete="off"
                    class="ant-select-selection-search-input"
                    id="rc_select_TEST_OR_SSR"
                    role="combobox"
                    type="search"
                    value=""
                  />
                </span>
                <span
                  class="ant-select-selection-item"
                  title="Organization 1"
                >
                  Organization 1
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select: none;"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width=
Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="css-dev-only-do-not-override-1f507pn ant-app"
    >
      <div
        data-testid="user-loaded"
      />
      <div
        class="ant-layout css-dev-only-do-not-override-1f507pn"
        style="min-height: 100vh;"
      >
        <div
          class="_container_2fb522"
        >
          <div
            class="_leftSide_2fb522"
          >
            <svg
              class="_logo_2fb522"
              id="Layer_1"
              style="enable-background: new 0 0 422.5 77.4;"
              viewBox="0 0 422.5 77.4"
              x="0px"
              xml:space="preserve"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              y="0px"
            >
              <g>
                <path
                  d="M58.2,52.4c0,14.5-11.4,24.3-28.4,24.3C13.4,76.6,2.4,67.8,0,52.8l17.7-2.2c1.6,6,5.4,8.7,12.3,8.7c9.9,0,9.9-4.4,9.9-6.5 c0-3.2,0-4.1-12.6-7.1C14.4,42.6,2.5,37.7,2.5,23c0-11.2,8.2-23.8,26.7-23.8c18.5,0,26.5,12.3,27.6,22.7l-17.7,1.9 c-0.9-4.7-4.4-7.3-10.3-7.3c-8.4,0-8.4,4.6-8.4,5.8c0,1.7,0,3.5,11,6.2C50.6,33.1,58.2,39,58.2,52.4z"
                />
                <path
                  d="M330.8,2.5L330.8,2.5c-0.1-0.1-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0c-1.3-0.1-2.6-0.2-4-0.2 c0,0,0,0,0,0s0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10c-1.8-0.3-3.6-0.5-5.5-0.5 c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3c-0.1,0-0.1,0.1-0.2,0.1l0,0 c-3.6,1.8-6.8,4.2-9.5,7.2c-2.7-3-5.9-5.5-9.5-7.2l0,0c-0.1,0-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0 c-1.3-0.1-2.6-0.2-4-0.2c0,0,0,0,0,0c0,0,0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10 c-1.8-0.3-3.6-0.5-5.5-0.5c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3 c-0.1,0-0.1,0.1-0.2,0.1l0,0C144,8.3,136.8,21.1,136.8,38v27.2v9.2h18.9V38c0-1.5,0.1-3.5,0.4-5.6c1-6.4,4.3-14.2,14.4-14.2 c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2 c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4,0.4,5.5c0,0,0,0.1,0,0.1v27.2v9.2h0h18.9h0v-2.6V38c0,0,0-0.1,0-0.1 c0-1.5,0.1-3.4,0.4-5.5c1-6.4,4.3-14.2,14.4-14.2c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38 c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4.1,0.4,5.6v36.4h18.9v-2.6V38 C349.7,21.1,342.5,8.3,330.8,2.5z"
                />
                <g>
                  <path
                    d="M112.5,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H64v13.9c0,23.2,13.6,38.8,33.8,38.8c0,0,0,0,0,0 c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H112.5z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="88.2"
                    y="47.9"
                  />
                </g>
                <g>
                  <path
                    d="M403.6,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H355v13.9c0,23.2,13.6,38.8,33.8,38.8 c0,0,0,0,0,0c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H403.6z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="379.3"
                    y="47.9"
                  />
                </g>
              </g>
            </svg>
            <div
              class="ant-select ant-select-outlined _select_270c7e css-dev-only-do-not-override-1f507pn ant-select-single ant-select-show-arrow ant-select-show-search"
              data-testid="organization-select"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-search"
                >
                  <input
                    aria-autocomplete="list"
                    aria-controls="rc_select_TEST_OR_SSR_list"
                    aria-expanded="false"
                    aria-haspopup="listbox"
                    aria-owns="rc_select_TEST_OR_SSR_list"
                    autocomplete="off"
                    class="ant-select-selection-search-input"
                    id="rc_select_TEST_OR_SSR"
                    role="combobox"
                    type="search"
                    value=""
                  />
                </span>
                <span
                  class="ant-select-selection-item"
                  title="Organization 1"
                >
                  Organization 1
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select: none;"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width=
⎯⎯⎯⎯⎯⎯⎯ Failed Tests 3 ⎯⎯⎯⎯⎯⎯⎯
 FAIL  src/pages/ProfilePage.test.tsx > ProfilePage > should display organizations
TestingLibraryElementError: Unable to find an element by: [data-testid="organization-item"]
Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="css-dev-only-do-not-override-1f507pn ant-app"
    >
      <div
        data-testid="user-loaded"
      />
      <div
        class="ant-layout css-dev-only-do-not-override-1f507pn"
        style="min-height: 100vh;"
      >
        <div
          class="_container_2fb522"
        >
          <div
            class="_leftSide_2fb522"
          >
            <svg
              class="_logo_2fb522"
              id="Layer_1"
              style="enable-background: new 0 0 422.5 77.4;"
              viewBox="0 0 422.5 77.4"
              x="0px"
              xml:space="preserve"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              y="0px"
            >
              <g>
                <path
                  d="M58.2,52.4c0,14.5-11.4,24.3-28.4,24.3C13.4,76.6,2.4,67.8,0,52.8l17.7-2.2c1.6,6,5.4,8.7,12.3,8.7c9.9,0,9.9-4.4,9.9-6.5 c0-3.2,0-4.1-12.6-7.1C14.4,42.6,2.5,37.7,2.5,23c0-11.2,8.2-23.8,26.7-23.8c18.5,0,26.5,12.3,27.6,22.7l-17.7,1.9 c-0.9-4.7-4.4-7.3-10.3-7.3c-8.4,0-8.4,4.6-8.4,5.8c0,1.7,0,3.5,11,6.2C50.6,33.1,58.2,39,58.2,52.4z"
                />
                <path
                  d="M330.8,2.5L330.8,2.5c-0.1-0.1-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0c-1.3-0.1-2.6-0.2-4-0.2 c0,0,0,0,0,0s0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10c-1.8-0.3-3.6-0.5-5.5-0.5 c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3c-0.1,0-0.1,0.1-0.2,0.1l0,0 c-3.6,1.8-6.8,4.2-9.5,7.2c-2.7-3-5.9-5.5-9.5-7.2l0,0c-0.1,0-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0 c-1.3-0.1-2.6-0.2-4-0.2c0,0,0,0,0,0c0,0,0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10 c-1.8-0.3-3.6-0.5-5.5-0.5c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3 c-0.1,0-0.1,0.1-0.2,0.1l0,0C144,8.3,136.8,21.1,136.8,38v27.2v9.2h18.9V38c0-1.5,0.1-3.5,0.4-5.6c1-6.4,4.3-14.2,14.4-14.2 c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2 c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4,0.4,5.5c0,0,0,0.1,0,0.1v27.2v9.2h0h18.9h0v-2.6V38c0,0,0-0.1,0-0.1 c0-1.5,0.1-3.4,0.4-5.5c1-6.4,4.3-14.2,14.4-14.2c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38 c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4.1,0.4,5.6v36.4h18.9v-2.6V38 C349.7,21.1,342.5,8.3,330.8,2.5z"
                />
                <g>
                  <path
                    d="M112.5,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H64v13.9c0,23.2,13.6,38.8,33.8,38.8c0,0,0,0,0,0 c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H112.5z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="88.2"
                    y="47.9"
                  />
                </g>
                <g>
                  <path
                    d="M403.6,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H355v13.9c0,23.2,13.6,38.8,33.8,38.8 c0,0,0,0,0,0c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H403.6z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="379.3"
                    y="47.9"
                  />
                </g>
              </g>
            </svg>
            <div
              class="ant-select ant-select-outlined _select_270c7e css-dev-only-do-not-override-1f507pn ant-select-single ant-select-show-arrow ant-select-show-search"
              data-testid="organization-select"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-search"
                >
                  <input
                    aria-autocomplete="list"
                    aria-controls="rc_select_TEST_OR_SSR_list"
                    aria-expanded="false"
                    aria-haspopup="listbox"
                    aria-owns="rc_select_TEST_OR_SSR_list"
                    autocomplete="off"
                    class="ant-select-selection-search-input"
                    id="rc_select_TEST_OR_SSR"
                    role="combobox"
                    type="search"
                    value=""
                  />
                </span>
                <span
                  class="ant-select-selection-item"
                  title="Organization 1"
                >
                  Organization 1
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select: none;"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width=
Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="css-dev-only-do-not-override-1f507pn ant-app"
    >
      <div
        data-testid="user-loaded"
      />
      <div
        class="ant-layout css-dev-only-do-not-override-1f507pn"
        style="min-height: 100vh;"
      >
        <div
          class="_container_2fb522"
        >
          <div
            class="_leftSide_2fb522"
          >
            <svg
              class="_logo_2fb522"
              id="Layer_1"
              style="enable-background: new 0 0 422.5 77.4;"
              viewBox="0 0 422.5 77.4"
              x="0px"
              xml:space="preserve"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              y="0px"
            >
              <g>
                <path
                  d="M58.2,52.4c0,14.5-11.4,24.3-28.4,24.3C13.4,76.6,2.4,67.8,0,52.8l17.7-2.2c1.6,6,5.4,8.7,12.3,8.7c9.9,0,9.9-4.4,9.9-6.5 c0-3.2,0-4.1-12.6-7.1C14.4,42.6,2.5,37.7,2.5,23c0-11.2,8.2-23.8,26.7-23.8c18.5,0,26.5,12.3,27.6,22.7l-17.7,1.9 c-0.9-4.7-4.4-7.3-10.3-7.3c-8.4,0-8.4,4.6-8.4,5.8c0,1.7,0,3.5,11,6.2C50.6,33.1,58.2,39,58.2,52.4z"
                />
                <path
                  d="M330.8,2.5L330.8,2.5c-0.1-0.1-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0c-1.3-0.1-2.6-0.2-4-0.2 c0,0,0,0,0,0s0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10c-1.8-0.3-3.6-0.5-5.5-0.5 c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3c-0.1,0-0.1,0.1-0.2,0.1l0,0 c-3.6,1.8-6.8,4.2-9.5,7.2c-2.7-3-5.9-5.5-9.5-7.2l0,0c-0.1,0-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0 c-1.3-0.1-2.6-0.2-4-0.2c0,0,0,0,0,0c0,0,0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10 c-1.8-0.3-3.6-0.5-5.5-0.5c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3 c-0.1,0-0.1,0.1-0.2,0.1l0,0C144,8.3,136.8,21.1,136.8,38v27.2v9.2h18.9V38c0-1.5,0.1-3.5,0.4-5.6c1-6.4,4.3-14.2,14.4-14.2 c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2 c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4,0.4,5.5c0,0,0,0.1,0,0.1v27.2v9.2h0h18.9h0v-2.6V38c0,0,0-0.1,0-0.1 c0-1.5,0.1-3.4,0.4-5.5c1-6.4,4.3-14.2,14.4-14.2c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38 c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4.1,0.4,5.6v36.4h18.9v-2.6V38 C349.7,21.1,342.5,8.3,330.8,2.5z"
                />
                <g>
                  <path
                    d="M112.5,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H64v13.9c0,23.2,13.6,38.8,33.8,38.8c0,0,0,0,0,0 c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H112.5z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="88.2"
                    y="47.9"
                  />
                </g>
                <g>
                  <path
                    d="M403.6,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H355v13.9c0,23.2,13.6,38.8,33.8,38.8 c0,0,0,0,0,0c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H403.6z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="379.3"
                    y="47.9"
                  />
                </g>
              </g>
            </svg>
            <div
              class="ant-select ant-select-outlined _select_270c7e css-dev-only-do-not-override-1f507pn ant-select-single ant-select-show-arrow ant-select-show-search"
              data-testid="organization-select"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-search"
                >
                  <input
                    aria-autocomplete="list"
                    aria-controls="rc_select_TEST_OR_SSR_list"
                    aria-expanded="false"
                    aria-haspopup="listbox"
                    aria-owns="rc_select_TEST_OR_SSR_list"
                    autocomplete="off"
                    class="ant-select-selection-search-input"
                    id="rc_select_TEST_OR_SSR"
                    role="combobox"
                    type="search"
                    value=""
                  />
                </span>
                <span
                  class="ant-select-selection-item"
                  title="Organization 1"
                >
                  Organization 1
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select: none;"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width= ❯ waitForWrapper node_modules/@testing-library/dom/dist/wait-for.js:160:27
 ❯ findAllByTestId node_modules/@testing-library/dom/dist/query-helpers.js:86:33
 ❯ src/pages/ProfilePage.test.tsx:200:44
    198|     fireEvent.click(organizationsTab);
    199|
    200|     const organizationItems = await screen.findAllByTestId('organizati…
       |                                            ^
    201|     expect(organizationItems).toHaveLength(2);
    202|   });
⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[1/3]⎯
 FAIL  src/pages/ProfilePage.test.tsx > ProfilePage > should leave organization
TestingLibraryElementError: Unable to find an element by: [data-testid="leave-organization-button"]
Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="css-dev-only-do-not-override-1f507pn ant-app"
    >
      <div
        data-testid="user-loaded"
      />
      <div
        class="ant-layout css-dev-only-do-not-override-1f507pn"
        style="min-height: 100vh;"
      >
        <div
          class="_container_2fb522"
        >
          <div
            class="_leftSide_2fb522"
          >
            <svg
              class="_logo_2fb522"
              id="Layer_1"
              style="enable-background: new 0 0 422.5 77.4;"
              viewBox="0 0 422.5 77.4"
              x="0px"
              xml:space="preserve"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              y="0px"
            >
              <g>
                <path
                  d="M58.2,52.4c0,14.5-11.4,24.3-28.4,24.3C13.4,76.6,2.4,67.8,0,52.8l17.7-2.2c1.6,6,5.4,8.7,12.3,8.7c9.9,0,9.9-4.4,9.9-6.5 c0-3.2,0-4.1-12.6-7.1C14.4,42.6,2.5,37.7,2.5,23c0-11.2,8.2-23.8,26.7-23.8c18.5,0,26.5,12.3,27.6,22.7l-17.7,1.9 c-0.9-4.7-4.4-7.3-10.3-7.3c-8.4,0-8.4,4.6-8.4,5.8c0,1.7,0,3.5,11,6.2C50.6,33.1,58.2,39,58.2,52.4z"
                />
                <path
                  d="M330.8,2.5L330.8,2.5c-0.1-0.1-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0c-1.3-0.1-2.6-0.2-4-0.2 c0,0,0,0,0,0s0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10c-1.8-0.3-3.6-0.5-5.5-0.5 c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3c-0.1,0-0.1,0.1-0.2,0.1l0,0 c-3.6,1.8-6.8,4.2-9.5,7.2c-2.7-3-5.9-5.5-9.5-7.2l0,0c-0.1,0-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0 c-1.3-0.1-2.6-0.2-4-0.2c0,0,0,0,0,0c0,0,0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10 c-1.8-0.3-3.6-0.5-5.5-0.5c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3 c-0.1,0-0.1,0.1-0.2,0.1l0,0C144,8.3,136.8,21.1,136.8,38v27.2v9.2h18.9V38c0-1.5,0.1-3.5,0.4-5.6c1-6.4,4.3-14.2,14.4-14.2 c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2 c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4,0.4,5.5c0,0,0,0.1,0,0.1v27.2v9.2h0h18.9h0v-2.6V38c0,0,0-0.1,0-0.1 c0-1.5,0.1-3.4,0.4-5.5c1-6.4,4.3-14.2,14.4-14.2c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38 c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4.1,0.4,5.6v36.4h18.9v-2.6V38 C349.7,21.1,342.5,8.3,330.8,2.5z"
                />
                <g>
                  <path
                    d="M112.5,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H64v13.9c0,23.2,13.6,38.8,33.8,38.8c0,0,0,0,0,0 c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H112.5z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="88.2"
                    y="47.9"
                  />
                </g>
                <g>
                  <path
                    d="M403.6,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H355v13.9c0,23.2,13.6,38.8,33.8,38.8 c0,0,0,0,0,0c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H403.6z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="379.3"
                    y="47.9"
                  />
                </g>
              </g>
            </svg>
            <div
              class="ant-select ant-select-outlined _select_270c7e css-dev-only-do-not-override-1f507pn ant-select-single ant-select-show-arrow ant-select-show-search"
              data-testid="organization-select"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-search"
                >
                  <input
                    aria-autocomplete="list"
                    aria-controls="rc_select_TEST_OR_SSR_list"
                    aria-expanded="false"
                    aria-haspopup="listbox"
                    aria-owns="rc_select_TEST_OR_SSR_list"
                    autocomplete="off"
                    class="ant-select-selection-search-input"
                    id="rc_select_TEST_OR_SSR"
                    role="combobox"
                    type="search"
                    value=""
                  />
                </span>
                <span
                  class="ant-select-selection-item"
                  title="Organization 1"
                >
                  Organization 1
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select: none;"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width=
Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="css-dev-only-do-not-override-1f507pn ant-app"
    >
      <div
        data-testid="user-loaded"
      />
      <div
        class="ant-layout css-dev-only-do-not-override-1f507pn"
        style="min-height: 100vh;"
      >
        <div
          class="_container_2fb522"
        >
          <div
            class="_leftSide_2fb522"
          >
            <svg
              class="_logo_2fb522"
              id="Layer_1"
              style="enable-background: new 0 0 422.5 77.4;"
              viewBox="0 0 422.5 77.4"
              x="0px"
              xml:space="preserve"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              y="0px"
            >
              <g>
                <path
                  d="M58.2,52.4c0,14.5-11.4,24.3-28.4,24.3C13.4,76.6,2.4,67.8,0,52.8l17.7-2.2c1.6,6,5.4,8.7,12.3,8.7c9.9,0,9.9-4.4,9.9-6.5 c0-3.2,0-4.1-12.6-7.1C14.4,42.6,2.5,37.7,2.5,23c0-11.2,8.2-23.8,26.7-23.8c18.5,0,26.5,12.3,27.6,22.7l-17.7,1.9 c-0.9-4.7-4.4-7.3-10.3-7.3c-8.4,0-8.4,4.6-8.4,5.8c0,1.7,0,3.5,11,6.2C50.6,33.1,58.2,39,58.2,52.4z"
                />
                <path
                  d="M330.8,2.5L330.8,2.5c-0.1-0.1-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0c-1.3-0.1-2.6-0.2-4-0.2 c0,0,0,0,0,0s0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10c-1.8-0.3-3.6-0.5-5.5-0.5 c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3c-0.1,0-0.1,0.1-0.2,0.1l0,0 c-3.6,1.8-6.8,4.2-9.5,7.2c-2.7-3-5.9-5.5-9.5-7.2l0,0c-0.1,0-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0 c-1.3-0.1-2.6-0.2-4-0.2c0,0,0,0,0,0c0,0,0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10 c-1.8-0.3-3.6-0.5-5.5-0.5c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3 c-0.1,0-0.1,0.1-0.2,0.1l0,0C144,8.3,136.8,21.1,136.8,38v27.2v9.2h18.9V38c0-1.5,0.1-3.5,0.4-5.6c1-6.4,4.3-14.2,14.4-14.2 c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2 c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4,0.4,5.5c0,0,0,0.1,0,0.1v27.2v9.2h0h18.9h0v-2.6V38c0,0,0-0.1,0-0.1 c0-1.5,0.1-3.4,0.4-5.5c1-6.4,4.3-14.2,14.4-14.2c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38 c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4.1,0.4,5.6v36.4h18.9v-2.6V38 C349.7,21.1,342.5,8.3,330.8,2.5z"
                />
                <g>
                  <path
                    d="M112.5,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H64v13.9c0,23.2,13.6,38.8,33.8,38.8c0,0,0,0,0,0 c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H112.5z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="88.2"
                    y="47.9"
                  />
                </g>
                <g>
                  <path
                    d="M403.6,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H355v13.9c0,23.2,13.6,38.8,33.8,38.8 c0,0,0,0,0,0c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H403.6z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="379.3"
                    y="47.9"
                  />
                </g>
              </g>
            </svg>
            <div
              class="ant-select ant-select-outlined _select_270c7e css-dev-only-do-not-override-1f507pn ant-select-single ant-select-show-arrow ant-select-show-search"
              data-testid="organization-select"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-search"
                >
                  <input
                    aria-autocomplete="list"
                    aria-controls="rc_select_TEST_OR_SSR_list"
                    aria-expanded="false"
                    aria-haspopup="listbox"
                    aria-owns="rc_select_TEST_OR_SSR_list"
                    autocomplete="off"
                    class="ant-select-selection-search-input"
                    id="rc_select_TEST_OR_SSR"
                    role="combobox"
                    type="search"
                    value=""
                  />
                </span>
                <span
                  class="ant-select-selection-item"
                  title="Organization 1"
                >
                  Organization 1
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select: none;"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width= ❯ waitForWrapper node_modules/@testing-library/dom/dist/wait-for.js:160:27
 ❯ findAllByTestId node_modules/@testing-library/dom/dist/query-helpers.js:86:33
 ❯ src/pages/ProfilePage.test.tsx:212:39
    210|     fireEvent.click(organizationsTab);
    211|
    212|     const leaveButtons = await screen.findAllByTestId('leave-organizat…
       |                                       ^
    213|
    214|     fireEvent.click(leaveButtons[0]);
⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[2/3]⎯
 FAIL  src/pages/ProfilePage.test.tsx > ProfilePage > should display leave organization error
TestingLibraryElementError: Unable to find an element by: [data-testid="leave-organization-button"]
Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="css-dev-only-do-not-override-1f507pn ant-app"
    >
      <div
        data-testid="user-loaded"
      />
      <div
        class="ant-layout css-dev-only-do-not-override-1f507pn"
        style="min-height: 100vh;"
      >
        <div
          class="_container_2fb522"
        >
          <div
            class="_leftSide_2fb522"
          >
            <svg
              class="_logo_2fb522"
              id="Layer_1"
              style="enable-background: new 0 0 422.5 77.4;"
              viewBox="0 0 422.5 77.4"
              x="0px"
              xml:space="preserve"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              y="0px"
            >
              <g>
                <path
                  d="M58.2,52.4c0,14.5-11.4,24.3-28.4,24.3C13.4,76.6,2.4,67.8,0,52.8l17.7-2.2c1.6,6,5.4,8.7,12.3,8.7c9.9,0,9.9-4.4,9.9-6.5 c0-3.2,0-4.1-12.6-7.1C14.4,42.6,2.5,37.7,2.5,23c0-11.2,8.2-23.8,26.7-23.8c18.5,0,26.5,12.3,27.6,22.7l-17.7,1.9 c-0.9-4.7-4.4-7.3-10.3-7.3c-8.4,0-8.4,4.6-8.4,5.8c0,1.7,0,3.5,11,6.2C50.6,33.1,58.2,39,58.2,52.4z"
                />
                <path
                  d="M330.8,2.5L330.8,2.5c-0.1-0.1-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0c-1.3-0.1-2.6-0.2-4-0.2 c0,0,0,0,0,0s0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10c-1.8-0.3-3.6-0.5-5.5-0.5 c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3c-0.1,0-0.1,0.1-0.2,0.1l0,0 c-3.6,1.8-6.8,4.2-9.5,7.2c-2.7-3-5.9-5.5-9.5-7.2l0,0c-0.1,0-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0 c-1.3-0.1-2.6-0.2-4-0.2c0,0,0,0,0,0c0,0,0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10 c-1.8-0.3-3.6-0.5-5.5-0.5c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3 c-0.1,0-0.1,0.1-0.2,0.1l0,0C144,8.3,136.8,21.1,136.8,38v27.2v9.2h18.9V38c0-1.5,0.1-3.5,0.4-5.6c1-6.4,4.3-14.2,14.4-14.2 c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2 c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4,0.4,5.5c0,0,0,0.1,0,0.1v27.2v9.2h0h18.9h0v-2.6V38c0,0,0-0.1,0-0.1 c0-1.5,0.1-3.4,0.4-5.5c1-6.4,4.3-14.2,14.4-14.2c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38 c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4.1,0.4,5.6v36.4h18.9v-2.6V38 C349.7,21.1,342.5,8.3,330.8,2.5z"
                />
                <g>
                  <path
                    d="M112.5,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H64v13.9c0,23.2,13.6,38.8,33.8,38.8c0,0,0,0,0,0 c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H112.5z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="88.2"
                    y="47.9"
                  />
                </g>
                <g>
                  <path
                    d="M403.6,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H355v13.9c0,23.2,13.6,38.8,33.8,38.8 c0,0,0,0,0,0c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H403.6z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="379.3"
                    y="47.9"
                  />
                </g>
              </g>
            </svg>
            <div
              class="ant-select ant-select-outlined _select_270c7e css-dev-only-do-not-override-1f507pn ant-select-single ant-select-show-arrow ant-select-show-search"
              data-testid="organization-select"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-search"
                >
                  <input
                    aria-autocomplete="list"
                    aria-controls="rc_select_TEST_OR_SSR_list"
                    aria-expanded="false"
                    aria-haspopup="listbox"
                    aria-owns="rc_select_TEST_OR_SSR_list"
                    autocomplete="off"
                    class="ant-select-selection-search-input"
                    id="rc_select_TEST_OR_SSR"
                    role="combobox"
                    type="search"
                    value=""
                  />
                </span>
                <span
                  class="ant-select-selection-item"
                  title="Organization 1"
                >
                  Organization 1
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select: none;"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width=
Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="css-dev-only-do-not-override-1f507pn ant-app"
    >
      <div
        data-testid="user-loaded"
      />
      <div
        class="ant-layout css-dev-only-do-not-override-1f507pn"
        style="min-height: 100vh;"
      >
        <div
          class="_container_2fb522"
        >
          <div
            class="_leftSide_2fb522"
          >
            <svg
              class="_logo_2fb522"
              id="Layer_1"
              style="enable-background: new 0 0 422.5 77.4;"
              viewBox="0 0 422.5 77.4"
              x="0px"
              xml:space="preserve"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              y="0px"
            >
              <g>
                <path
                  d="M58.2,52.4c0,14.5-11.4,24.3-28.4,24.3C13.4,76.6,2.4,67.8,0,52.8l17.7-2.2c1.6,6,5.4,8.7,12.3,8.7c9.9,0,9.9-4.4,9.9-6.5 c0-3.2,0-4.1-12.6-7.1C14.4,42.6,2.5,37.7,2.5,23c0-11.2,8.2-23.8,26.7-23.8c18.5,0,26.5,12.3,27.6,22.7l-17.7,1.9 c-0.9-4.7-4.4-7.3-10.3-7.3c-8.4,0-8.4,4.6-8.4,5.8c0,1.7,0,3.5,11,6.2C50.6,33.1,58.2,39,58.2,52.4z"
                />
                <path
                  d="M330.8,2.5L330.8,2.5c-0.1-0.1-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0c-1.3-0.1-2.6-0.2-4-0.2 c0,0,0,0,0,0s0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10c-1.8-0.3-3.6-0.5-5.5-0.5 c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3c-0.1,0-0.1,0.1-0.2,0.1l0,0 c-3.6,1.8-6.8,4.2-9.5,7.2c-2.7-3-5.9-5.5-9.5-7.2l0,0c-0.1,0-0.1-0.1-0.2-0.1c-3.2-1.5-6.7-2.5-10.4-3c-0.1,0-0.1,0-0.2,0 c-1.3-0.1-2.6-0.2-4-0.2c0,0,0,0,0,0c0,0,0,0,0,0c-1.9,0-3.6,0.2-5.4,0.5c-7.5,1.2-14,4.7-18.8,10c-4.8-5.3-11.2-8.8-18.7-10 c-1.8-0.3-3.6-0.5-5.5-0.5c0,0,0,0,0,0c0,0,0,0,0,0c-1.4,0-2.7,0.1-4,0.2c-0.1,0-0.1,0-0.2,0c-3.8,0.4-7.3,1.4-10.4,3 c-0.1,0-0.1,0.1-0.2,0.1l0,0C144,8.3,136.8,21.1,136.8,38v27.2v9.2h18.9V38c0-1.5,0.1-3.5,0.4-5.6c1-6.4,4.3-14.2,14.4-14.2 c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2 c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4,0.4,5.5c0,0,0,0.1,0,0.1v27.2v9.2h0h18.9h0v-2.6V38c0,0,0-0.1,0-0.1 c0-1.5,0.1-3.4,0.4-5.5c1-6.4,4.3-14.2,14.4-14.2c0.9,0,1.7,0.1,2.5,0.2c10.9,1.6,12.2,14.1,12.2,19.7v36.4h0.2h18.8h0.2V38 c0-5.6,1.2-18.1,12.2-19.7c0.8-0.1,1.6-0.2,2.5-0.2c10.1,0,13.4,7.8,14.4,14.2c0.3,2.1,0.4,4.1,0.4,5.6v36.4h18.9v-2.6V38 C349.7,21.1,342.5,8.3,330.8,2.5z"
                />
                <g>
                  <path
                    d="M112.5,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H64v13.9c0,23.2,13.6,38.8,33.8,38.8c0,0,0,0,0,0 c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H112.5z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="88.2"
                    y="47.9"
                  />
                </g>
                <g>
                  <path
                    d="M403.6,1.4v13.9c0,6-1.6,19.9-14.8,19.9c-13.2,0-14.8-13.9-14.8-19.9V1.4H355v13.9c0,23.2,13.6,38.8,33.8,38.8 c0,0,0,0,0,0c0,0,0,0,0,0c20.2,0,33.8-15.6,33.8-38.8V1.4H403.6z"
                  />
                  <rect
                    height="26.5"
                    width="19"
                    x="379.3"
                    y="47.9"
                  />
                </g>
              </g>
            </svg>
            <div
              class="ant-select ant-select-outlined _select_270c7e css-dev-only-do-not-override-1f507pn ant-select-single ant-select-show-arrow ant-select-show-search"
              data-testid="organization-select"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-search"
                >
                  <input
                    aria-autocomplete="list"
                    aria-controls="rc_select_TEST_OR_SSR_list"
                    aria-expanded="false"
                    aria-haspopup="listbox"
                    aria-owns="rc_select_TEST_OR_SSR_list"
                    autocomplete="off"
                    class="ant-select-selection-search-input"
                    id="rc_select_TEST_OR_SSR"
                    role="combobox"
                    type="search"
                    value=""
                  />
                </span>
                <span
                  class="ant-select-selection-item"
                  title="Organization 1"
                >
                  Organization 1
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select: none;"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width= ❯ waitForWrapper node_modules/@testing-library/dom/dist/wait-for.js:160:27
 ❯ findAllByTestId node_modules/@testing-library/dom/dist/query-helpers.js:86:33
 ❯ src/pages/ProfilePage.test.tsx:236:39
    234|     fireEvent.click(organizationsTab);
    235|
    236|     const leaveButtons = await screen.findAllByTestId('leave-organizat…
       |                                       ^
    237|
    238|     fireEvent.click(leaveButtons[0]);
⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[3/3]⎯
 Test Files  1 failed | 10 passed (11)
      Tests  3 failed | 44 passed (47)
   Start at  10:39:03
   Duration  71.01s (transform 1.75s, setup 13.50s, collect 116.47s, tests 136.39s, environment 20.98s, prepare 3.65s)
npm notice
npm notice New major version of npm available! 10.8.2 -> 11.4.1
npm notice Changelog: https://github.com/npm/cli/releases/tag/v11.4.1
npm notice To update run: npm install -g npm@11.4.1
npm notice
Cleaning up project directory and file based variables 00:01
ERROR: Job failed: exit status 1
tus 1