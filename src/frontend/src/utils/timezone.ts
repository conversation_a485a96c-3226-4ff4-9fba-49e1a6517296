import moment from 'moment';
import 'moment-timezone';

/**
 * Timezone utilities for converting and formatting datetimes
 * based on organization timezone preferences.
 */

/**
 * Convert a UTC datetime string to the specified timezone
 * @param utcDatetime - UTC datetime string (ISO format)
 * @param timezone - Target timezone (e.g., 'America/New_York')
 * @returns moment object in the target timezone
 */
export const convertUtcToTimezone = (utcDatetime: string, timezone: string = 'UTC'): moment.Moment => {
  if (!utcDatetime) {
    return moment();
  }
  
  try {
    // Parse as UTC and convert to target timezone
    return moment.utc(utcDatetime).tz(timezone);
  } catch (error) {
    console.warn(`Failed to convert datetime to timezone ${timezone}:`, error);
    // Fallback to UTC
    return moment.utc(utcDatetime);
  }
};

/**
 * Convert a datetime from a specific timezone to UTC
 * @param localDatetime - Local datetime string
 * @param timezone - Source timezone
 * @returns moment object in UTC
 */
export const convertTimezoneToUtc = (localDatetime: string, timezone: string = 'UTC'): moment.Moment => {
  if (!localDatetime) {
    return moment.utc();
  }
  
  try {
    // Parse in the source timezone and convert to UTC
    return moment.tz(localDatetime, timezone).utc();
  } catch (error) {
    console.warn(`Failed to convert datetime from timezone ${timezone} to UTC:`, error);
    // Fallback to parsing as UTC
    return moment.utc(localDatetime);
  }
};

/**
 * Format a UTC datetime string for display in the organization's timezone
 * @param utcDatetime - UTC datetime string
 * @param timezone - Organization timezone
 * @param format - Moment.js format string
 * @returns Formatted datetime string
 */
export const formatDatetimeForTimezone = (
  utcDatetime: string, 
  timezone: string = 'UTC', 
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string => {
  if (!utcDatetime) {
    return '';
  }
  
  const convertedDatetime = convertUtcToTimezone(utcDatetime, timezone);
  return convertedDatetime.format(format);
};

/**
 * Get timezone-aware versions of the existing date formatting functions
 */
export const createTimezoneAwareDateFormatters = (timezone: string = 'UTC') => {
  return {
    toStandardDate: (utcDatetime: string) => {
      const converted = convertUtcToTimezone(utcDatetime, timezone);
      return converted.format('YYYY-MM-DD');
    },
    
    toStandardDatetime: (utcDatetime: string) => {
      const converted = convertUtcToTimezone(utcDatetime, timezone);
      return converted.format('YYYY-MM-DDTHH:mm:ss');
    },
    
    dayMonthYear: (utcDatetime: string) => {
      const converted = convertUtcToTimezone(utcDatetime, timezone);
      return converted.format('L');
    },
    
    dayMonthYearHourMinute: (utcDatetime: string) => {
      const converted = convertUtcToTimezone(utcDatetime, timezone);
      return converted.format('L LT');
    },
    
    dayMonthYearHourMinuteSecond: (utcDatetime: string) => {
      const converted = convertUtcToTimezone(utcDatetime, timezone);
      return converted.format('LL LTS');
    },
    
    monthYear: (utcDatetime: string) => {
      const converted = convertUtcToTimezone(utcDatetime, timezone);
      const monthName = new Date(converted.toDate()).toLocaleString(converted.locale(), {
        month: 'long',
      });
      return monthName + ' ' + converted.format('YYYY');
    },
  };
};

/**
 * Get the current timezone offset for display
 * @param timezone - Timezone name
 * @returns Offset string (e.g., '+05:30', '-08:00')
 */
export const getTimezoneOffset = (timezone: string = 'UTC'): string => {
  try {
    const now = moment.tz(timezone);
    return now.format('Z');
  } catch (error) {
    console.warn(`Failed to get offset for timezone ${timezone}:`, error);
    return '+00:00';
  }
};

/**
 * Get a human-readable timezone display name
 * @param timezone - Timezone name
 * @returns Display name with abbreviation
 */
export const getTimezoneDisplayName = (timezone: string = 'UTC'): string => {
  try {
    const now = moment.tz(timezone);
    const abbreviation = now.format('z');
    const offset = now.format('Z');
    
    if (timezone.includes('/')) {
      const [region, city] = timezone.split('/');
      const cityFormatted = city.replace(/_/g, ' ');
      return `${region}/${cityFormatted} (${abbreviation} ${offset})`;
    }
    
    return `${timezone} (${abbreviation} ${offset})`;
  } catch (error) {
    console.warn(`Failed to get display name for timezone ${timezone}:`, error);
    return timezone;
  }
};

/**
 * Check if a timezone is valid
 * @param timezone - Timezone name to validate
 * @returns True if valid, false otherwise
 */
export const isValidTimezone = (timezone: string): boolean => {
  try {
    moment.tz.zone(timezone);
    return true;
  } catch (error) {
    return false;
  }
};
