import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Button, message, Divider } from 'antd';
import { msg, Trans } from '@lingui/macro';
import { useLingui } from '@lingui/react';

import { useActiveOrganization, useRequest, useDisplayResponseMessage } from '@hooks';
import { TimezoneSelector } from '@components/fields';
import { API } from '@config';

import styles from './OrganizationSettings.module.scss';

interface OrganizationSettingsFormData {
  name: string;
  email: string;
  timezone: string;
}

const OrganizationSettings: React.FC = () => {
  const { _ } = useLingui();
  const [form] = Form.useForm();
  const request = useRequest();
  const displayResponseMessage = useDisplayResponseMessage();
  const activeOrganization = useActiveOrganization();
  
  const [loading, setLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Initialize form with current organization data
  useEffect(() => {
    if (activeOrganization) {
      form.setFieldsValue({
        name: activeOrganization.name,
        email: activeOrganization.email,
        timezone: activeOrganization.timezone || 'UTC',
      });
    }
  }, [activeOrganization, form]);

  const handleFormChange = () => {
    setHasChanges(true);
  };

  const handleSave = async () => {
    if (!activeOrganization) {
      message.error(_(msg`No active organization selected`));
      return;
    }

    try {
      setLoading(true);
      const values = await form.validateFields();
      
      await request(API.CORE_ORGANIZATIONS_UPDATE(activeOrganization.uuid), {
        method: 'PATCH',
        data: values,
      });

      message.success(_(msg`Organization settings updated successfully`));
      setHasChanges(false);
      
      // Optionally refresh organization data
      // This would require updating the organization in the global state
      
    } catch (error) {
      console.error('Failed to update organization settings:', error);
      displayResponseMessage('organization.update.error');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    if (activeOrganization) {
      form.setFieldsValue({
        name: activeOrganization.name,
        email: activeOrganization.email,
        timezone: activeOrganization.timezone || 'UTC',
      });
      setHasChanges(false);
    }
  };

  if (!activeOrganization) {
    return (
      <Card title={<Trans>Organization Settings</Trans>}>
        <p>{_(msg`No organization selected`)}</p>
      </Card>
    );
  }

  return (
    <Card
      title={<Trans>Organization Settings</Trans>}
      extra={
        <div className={styles.cardActions}>
          {hasChanges && (
            <>
              <Button onClick={handleReset} disabled={loading}>
                <Trans>Reset</Trans>
              </Button>
              <Button
                type="primary"
                onClick={handleSave}
                loading={loading}
                style={{ marginLeft: 8 }}
              >
                <Trans>Save Changes</Trans>
              </Button>
            </>
          )}
        </div>
      }
    >
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleFormChange}
        className={styles.form}
      >
        <Form.Item
          label={_(msg`Organization Name`)}
          name="name"
          rules={[
            { required: true, message: _(msg`Organization name is required`) },
            { max: 255, message: _(msg`Organization name must be less than 255 characters`) },
          ]}
        >
          <Input placeholder={_(msg`Enter organization name`)} />
        </Form.Item>

        <Form.Item
          label={_(msg`Email`)}
          name="email"
          rules={[
            { required: true, message: _(msg`Email is required`) },
            { type: 'email', message: _(msg`Please enter a valid email address`) },
          ]}
        >
          <Input placeholder={_(msg`Enter organization email`)} />
        </Form.Item>

        <Divider />

        <Form.Item
          label={_(msg`Timezone`)}
          name="timezone"
          help={_(msg`All dates and times will be displayed in this timezone`)}
          rules={[
            { required: true, message: _(msg`Timezone is required`) },
          ]}
        >
          <TimezoneSelector placeholder={_(msg`Select organization timezone`)} />
        </Form.Item>
      </Form>

      <div className={styles.timezoneInfo}>
        <p className={styles.infoText}>
          <Trans>
            Changing the timezone will affect how all dates and times are displayed 
            throughout the application for all members of this organization.
          </Trans>
        </p>
        <p className={styles.infoText}>
          <Trans>
            All data is stored in UTC and will be converted to the selected timezone 
            for display purposes only.
          </Trans>
        </p>
      </div>
    </Card>
  );
};

export default OrganizationSettings;
