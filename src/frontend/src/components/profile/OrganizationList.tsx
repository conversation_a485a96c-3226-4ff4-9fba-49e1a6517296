import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, Popconfirm, Space, Tag } from 'antd';
import { EditOutlined } from '@ant-design/icons';
import { msg, Trans } from '@lingui/macro';
import { useLingui } from '@lingui/react';

import { useOrganizationsWithRole, useLeaveOrganization, useRefetchOptions } from '@hooks';
import { API } from '@config';
import OrganizationEditModal from './OrganizationEditModal';

import styles from './OrganizationList.module.scss';

const OrganizationList = () => {
  const { _ } = useLingui();
  const refetchOptions = useRefetchOptions();
  const leaveOrganization = useLeaveOrganization();

  const { data: organizations = [], isLoading: loading } = useOrganizationsWithRole();
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedOrganization, setSelectedOrganization] = useState<OrganizationWithRole | null>(null);

  const handleEditClick = (organization: OrganizationWithRole) => {
    setSelectedOrganization(organization);
    setEditModalVisible(true);
  };

  const handleEditSuccess = () => {
    // Refresh organizations list and global state
    refetchOptions();
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'owner':
        return 'gold';
      case 'admin':
        return 'blue';
      case 'member':
        return 'default';
      default:
        return 'default';
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'owner':
        return _(msg`Owner`);
      case 'admin':
        return _(msg`Admin`);
      case 'member':
        return _(msg`Member`);
      default:
        return role;
    }
  };

  return (
    <>
      <Card
        title={
          <span>
            <Trans>Organizations</Trans>
          </span>
        }
        loading={loading}
      >
        <div className={styles.content}>
          {organizations.map((organization: OrganizationWithRole) => (
            <div
              className={styles.organizationItem}
              key={organization.uuid}
              data-testid="organization-item"
            >
              <div className={styles.organizationInfo}>
                <span className={styles.organizationName}>{organization.name}</span>
                <Tag color={getRoleColor(organization.user_role)} className={styles.roleTag}>
                  {getRoleLabel(organization.user_role)}
                </Tag>
                {organization.timezone && organization.timezone !== 'UTC' && (
                  <Tag color="cyan" className={styles.timezoneTag}>
                    {organization.timezone}
                  </Tag>
                )}
              </div>
              <Space>
                {organization.can_edit && (
                  <Button
                    icon={<EditOutlined />}
                    onClick={() => handleEditClick(organization)}
                    data-testid="edit-organization-button"
                  >
                    <Trans>Edit</Trans>
                  </Button>
                )}
                <Popconfirm
                  title={_(msg`Are you sure you want to leave ${organization.name}?`)}
                  okText={<div data-testid="yes-button">Yes</div>}
                  cancelText="No"
                  onConfirm={() => leaveOrganization(organization)}
                >
                  <Button danger data-testid="leave-organization-button">
                    <Trans>Leave</Trans>
                  </Button>
                </Popconfirm>
              </Space>
            </div>
          ))}
        </div>
      </Card>

      <OrganizationEditModal
        organization={selectedOrganization}
        visible={editModalVisible}
        onClose={() => setEditModalVisible(false)}
        onSuccess={handleEditSuccess}
      />
    </>
  );
};

export default OrganizationList;
