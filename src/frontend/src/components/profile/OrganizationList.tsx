import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, Popconfirm, Space, Tag } from 'antd';
import { EditOutlined } from '@ant-design/icons';
import { msg, Trans } from '@lingui/macro';
import { useLingui } from '@lingui/react';

import { useOrganizations, useLeaveOrganization, useRequest, useRefetchOptions } from '@hooks';
import { API } from '@config';
import OrganizationEditModal from './OrganizationEditModal';

import styles from './OrganizationList.module.scss';

interface OrganizationWithRole extends Organization {
  user_role: string;
  can_edit: boolean;
}

const OrganizationList = () => {
  const { _ } = useLingui();
  const request = useRequest();
  const refetchOptions = useRefetchOptions();
  const leaveOrganization = useLeaveOrganization();

  const [organizations, setOrganizations] = useState<OrganizationWithRole[]>([]);
  const [loading, setLoading] = useState(true);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedOrganization, setSelectedOrganization] = useState<OrganizationWithRole | null>(null);

  // Fetch organizations with role information
  React.useEffect(() => {
    const fetchOrganizations = async () => {
      try {
        setLoading(true);
        const { data } = await request(API.CORE_ORGANIZATIONS_PROFILE_LIST());
        setOrganizations(data);
      } catch (error) {
        console.error('Failed to fetch organizations:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchOrganizations();
  }, [request]);

  const handleEditClick = (organization: OrganizationWithRole) => {
    setSelectedOrganization(organization);
    setEditModalVisible(true);
  };

  const handleEditSuccess = () => {
    // Refresh organizations list and global state
    refetchOptions();
    // Refresh local state
    const fetchOrganizations = async () => {
      try {
        const { data } = await request(API.CORE_ORGANIZATIONS_PROFILE_LIST());
        setOrganizations(data);
      } catch (error) {
        console.error('Failed to refresh organizations:', error);
      }
    };
    fetchOrganizations();
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'owner':
        return 'gold';
      case 'admin':
        return 'blue';
      case 'member':
        return 'default';
      default:
        return 'default';
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'owner':
        return _(msg`Owner`);
      case 'admin':
        return _(msg`Admin`);
      case 'member':
        return _(msg`Member`);
      default:
        return role;
    }
  };

  return (
    <>
      <Card
        title={
          <span>
            <Trans>Organizations</Trans>
          </span>
        }
        loading={loading}
      >
        <div className={styles.content}>
          {organizations.map((organization: OrganizationWithRole) => (
            <div
              className={styles.organizationItem}
              key={organization.uuid}
              data-testid="organization-item"
            >
              <div className={styles.organizationInfo}>
                <span className={styles.organizationName}>{organization.name}</span>
                <Tag color={getRoleColor(organization.user_role)} className={styles.roleTag}>
                  {getRoleLabel(organization.user_role)}
                </Tag>
                {organization.timezone && organization.timezone !== 'UTC' && (
                  <Tag color="cyan" className={styles.timezoneTag}>
                    {organization.timezone}
                  </Tag>
                )}
              </div>
              <Space>
                {organization.can_edit && (
                  <Button
                    icon={<EditOutlined />}
                    onClick={() => handleEditClick(organization)}
                    data-testid="edit-organization-button"
                  >
                    <Trans>Edit</Trans>
                  </Button>
                )}
                <Popconfirm
                  title={_(msg`Are you sure you want to leave ${organization.name}?`)}
                  okText={<div data-testid="yes-button">Yes</div>}
                  cancelText="No"
                  onConfirm={() => leaveOrganization(organization)}
                >
                  <Button danger data-testid="leave-organization-button">
                    <Trans>Leave</Trans>
                  </Button>
                </Popconfirm>
              </Space>
            </div>
          ))}
        </div>
      </Card>

      <OrganizationEditModal
        organization={selectedOrganization}
        visible={editModalVisible}
        onClose={() => setEditModalVisible(false)}
        onSuccess={handleEditSuccess}
      />
    </>
  );
};

export default OrganizationList;
