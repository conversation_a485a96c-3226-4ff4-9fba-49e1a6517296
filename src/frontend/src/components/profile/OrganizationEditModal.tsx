import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, message } from 'antd';
import { msg, Trans } from '@lingui/macro';
import { useLingui } from '@lingui/react';

import { useRequest, useDisplayResponseMessage } from '@hooks';
import { TimezoneSelector } from '@components/fields';
import { API } from '@config';

import styles from './OrganizationEditModal.module.scss';

interface OrganizationEditModalProps {
  organization: Organization | null;
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface OrganizationFormData {
  name: string;
  email: string;
  timezone: string;
}

const OrganizationEditModal: React.FC<OrganizationEditModalProps> = ({
  organization,
  visible,
  onClose,
  onSuccess,
}) => {
  const { _ } = useLingui();
  const [form] = Form.useForm();
  const request = useRequest();
  const displayResponseMessage = useDisplayResponseMessage();
  
  const [loading, setLoading] = useState(false);

  // Initialize form when organization changes
  useEffect(() => {
    if (organization && visible) {
      form.setFieldsValue({
        name: organization.name,
        email: organization.email,
        timezone: organization.timezone || 'UTC',
      });
    }
  }, [organization, visible, form]);

  const handleSave = async () => {
    if (!organization) {
      message.error(_(msg`No organization selected`));
      return;
    }

    try {
      setLoading(true);
      const values = await form.validateFields();
      
      await request(API.CORE_ORGANIZATIONS_UPDATE(organization.uuid), {
        method: 'PATCH',
        data: values,
      });

      message.success(_(msg`Organization settings updated successfully`));
      onSuccess();
      onClose();
      
    } catch (error) {
      console.error('Failed to update organization settings:', error);
      displayResponseMessage('organization.update.error');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onClose();
  };

  return (
    <Modal
      title={<Trans>Edit Organization Settings</Trans>}
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel} disabled={loading}>
          <Trans>Cancel</Trans>
        </Button>,
        <Button
          key="save"
          type="primary"
          onClick={handleSave}
          loading={loading}
        >
          <Trans>Save Changes</Trans>
        </Button>,
      ]}
      width={600}
      className={styles.modal}
    >
      <Form
        form={form}
        layout="vertical"
        className={styles.form}
      >
        <Form.Item
          label={_(msg`Organization Name`)}
          name="name"
          rules={[
            { required: true, message: _(msg`Organization name is required`) },
            { max: 255, message: _(msg`Organization name must be less than 255 characters`) },
          ]}
        >
          <Input placeholder={_(msg`Enter organization name`)} />
        </Form.Item>

        <Form.Item
          label={_(msg`Email`)}
          name="email"
          rules={[
            { required: true, message: _(msg`Email is required`) },
            { type: 'email', message: _(msg`Please enter a valid email address`) },
          ]}
        >
          <Input placeholder={_(msg`Enter organization email`)} />
        </Form.Item>

        <Form.Item
          label={_(msg`Timezone`)}
          name="timezone"
          help={_(msg`All dates and times will be displayed in this timezone for all organization members`)}
          rules={[
            { required: true, message: _(msg`Timezone is required`) },
          ]}
        >
          <TimezoneSelector placeholder={_(msg`Select organization timezone`)} />
        </Form.Item>
      </Form>

      <div className={styles.timezoneInfo}>
        <p className={styles.infoText}>
          <Trans>
            Changing the timezone will affect how all dates and times are displayed 
            throughout the application for all members of this organization.
          </Trans>
        </p>
        <p className={styles.infoText}>
          <Trans>
            All data is stored in UTC and will be converted to the selected timezone 
            for display purposes only.
          </Trans>
        </p>
      </div>
    </Modal>
  );
};

export default OrganizationEditModal;
