import React, { useState, useEffect } from 'react';
import { Select, Spin } from 'antd';
import { msg } from '@lingui/macro';
import { useLingui } from '@lingui/react';

import { useRequest } from '@hooks';
import { API } from '@config';

const { Option } = Select;

interface TimezoneOption {
  value: string;
  label: string;
  offset: string;
}

interface TimezoneSelectorProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  style?: React.CSSProperties;
  className?: string;
}

const TimezoneSelector: React.FC<TimezoneSelectorProps> = ({
  value,
  onChange,
  placeholder,
  disabled = false,
  style,
  className,
}) => {
  const { _ } = useLingui();
  const request = useRequest();
  const [timezones, setTimezones] = useState<TimezoneOption[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchValue, setSearchValue] = useState('');

  useEffect(() => {
    const fetchTimezones = async () => {
      try {
        setLoading(true);
        const { data } = await request(API.CORE_TIMEZONES());
        setTimezones(data);
      } catch (error) {
        console.error('Failed to fetch timezones:', error);
        // Fallback to common timezones if API fails
        setTimezones([
          { value: 'UTC', label: 'UTC (+00:00)', offset: '+00:00' },
          { value: 'America/New_York', label: 'America/New York (EST)', offset: '-05:00' },
          { value: 'America/Los_Angeles', label: 'America/Los Angeles (PST)', offset: '-08:00' },
          { value: 'Europe/London', label: 'Europe/London (GMT)', offset: '+00:00' },
          { value: 'Europe/Paris', label: 'Europe/Paris (CET)', offset: '+01:00' },
          { value: 'Asia/Tokyo', label: 'Asia/Tokyo (JST)', offset: '+09:00' },
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchTimezones();
  }, [request]);

  const filteredTimezones = timezones.filter((timezone) =>
    timezone.label.toLowerCase().includes(searchValue.toLowerCase()) ||
    timezone.value.toLowerCase().includes(searchValue.toLowerCase())
  );

  const handleSearch = (value: string) => {
    setSearchValue(value);
  };

  const handleChange = (selectedValue: string) => {
    if (onChange) {
      onChange(selectedValue);
    }
  };

  return (
    <Select
      value={value}
      onChange={handleChange}
      onSearch={handleSearch}
      placeholder={placeholder || _(msg`Select timezone`)}
      disabled={disabled}
      loading={loading}
      showSearch
      filterOption={false}
      style={style}
      className={className}
      notFoundContent={loading ? <Spin size="small" /> : _(msg`No timezones found`)}
    >
      {filteredTimezones.map((timezone) => (
        <Option key={timezone.value} value={timezone.value}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>{timezone.label}</span>
            <span style={{ color: '#999', fontSize: '12px', marginLeft: '8px' }}>
              {timezone.offset}
            </span>
          </div>
        </Option>
      ))}
    </Select>
  );
};

export default TimezoneSelector;
