"""
Custom serializer fields for timezone-aware datetime handling.
"""

from rest_framework import serializers
from django.utils import timezone
from core.timezone_utils import convert_utc_to_timezone


class TimezoneAwareDateTimeField(serializers.DateTimeField):
    """
    A DateTimeField that converts UTC datetimes to the organization's timezone
    when serializing data for API responses.
    
    This field expects the organization timezone to be passed in the serializer context
    under the key 'organization_timezone'.
    """
    
    def to_representation(self, value):
        """
        Convert UTC datetime to organization timezone for display.
        
        Args:
            value: UTC datetime from the database
            
        Returns:
            ISO formatted datetime string in organization timezone
        """
        if not value:
            return None
            
        # Get organization timezone from context
        organization_timezone = self.context.get('organization_timezone', 'UTC')
        
        # Convert to organization timezone
        converted_datetime = convert_utc_to_timezone(value, organization_timezone)
        
        if converted_datetime:
            # Return ISO format with timezone info
            return converted_datetime.isoformat()
        else:
            # Fallback to original datetime if conversion fails
            return super().to_representation(value)
    
    def to_internal_value(self, value):
        """
        Parse datetime input and ensure it's stored as UTC.
        
        Args:
            value: Datetime string from API request
            
        Returns:
            UTC datetime for database storage
        """
        # Parse the datetime using parent method
        parsed_datetime = super().to_internal_value(value)
        
        # Ensure it's in UTC for storage
        if parsed_datetime.tzinfo is None:
            # If naive, assume it's in UTC
            parsed_datetime = timezone.make_aware(parsed_datetime, timezone.utc)
        else:
            # Convert to UTC
            parsed_datetime = parsed_datetime.astimezone(timezone.utc)
            
        return parsed_datetime


class OrganizationTimezoneSerializerMixin:
    """
    Mixin for serializers that need to include organization timezone in context.
    
    This mixin automatically adds the organization's timezone to the serializer context
    so that TimezoneAwareDateTimeField can use it for conversions.
    """
    
    def get_organization_timezone(self):
        """
        Get the organization timezone for the current context.
        
        Returns:
            Organization timezone string or 'UTC' as fallback
        """
        # Try to get organization from the instance
        if hasattr(self.instance, 'organization'):
            return getattr(self.instance.organization, 'timezone', 'UTC')
        
        # Try to get organization from context (for nested serializers)
        if hasattr(self.context.get('view'), 'get_object'):
            try:
                obj = self.context['view'].get_object()
                if hasattr(obj, 'organization'):
                    return getattr(obj.organization, 'timezone', 'UTC')
            except:
                pass
        
        # Try to get from request user's active organization
        request = self.context.get('request')
        if request and hasattr(request, 'user') and request.user.is_authenticated:
            # This would require additional logic to determine user's active organization
            # For now, return UTC as fallback
            pass
            
        return 'UTC'
    
    def to_representation(self, instance):
        """
        Add organization timezone to context before serialization.
        """
        # Add organization timezone to context
        if 'organization_timezone' not in self.context:
            self.context['organization_timezone'] = self.get_organization_timezone()
            
        return super().to_representation(instance)
