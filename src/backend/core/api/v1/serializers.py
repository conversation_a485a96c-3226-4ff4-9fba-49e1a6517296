from rest_framework.serializers import Model<PERSON>erializer, ValidationError, Serializer<PERSON>ethod<PERSON>ield

from connections.api.v1.serializers import BasicConnectionSerializer
from core.models import Organization, Project, OrganizationUser
from core.timezone_utils import validate_timezone
from core.serializer_fields import TimezoneAwareDate<PERSON><PERSON><PERSON>ield, OrganizationTimezoneSerializerMixin


class BasicProjectSerializer(OrganizationTimezoneSerializerMixin, ModelSerializer):
    created_at = TimezoneAwareDateTimeField(read_only=True)
    updated_at = TimezoneAwareDateTimeField(read_only=True)

    class Meta:
        model = Project
        fields = ["uuid", "name", "created_at", "updated_at"]


class BasicOrganizationSerializer(ModelSerializer):
    created_at = TimezoneAwareDateTimeField(read_only=True)
    updated_at = TimezoneAwareDateTimeField(read_only=True)

    class Meta:
        model = Organization
        fields = ["uuid", "name", "email", "timezone", "created_at", "updated_at"]

    def get_organization_timezone(self):
        """Override to get timezone from the organization instance itself."""
        if self.instance:
            return getattr(self.instance, 'timezone', 'UTC')
        return 'UTC'

    def to_representation(self, instance):
        """Add organization timezone to context before serialization."""
        if 'organization_timezone' not in self.context:
            self.context['organization_timezone'] = getattr(instance, 'timezone', 'UTC')
        return super().to_representation(instance)


class OrganizationUpdateSerializer(ModelSerializer):
    class Meta:
        model = Organization
        fields = ["name", "email", "timezone"]

    def validate_timezone(self, value):
        """Validate that the timezone is a valid timezone name."""
        if not validate_timezone(value):
            raise ValidationError(f"'{value}' is not a valid timezone.")
        return value


class OrganizationWithRoleSerializer(ModelSerializer):
    """Serializer for organizations that includes the user's role in the organization."""
    created_at = TimezoneAwareDateTimeField(read_only=True)
    updated_at = TimezoneAwareDateTimeField(read_only=True)
    user_role = SerializerMethodField()
    can_edit = SerializerMethodField()

    class Meta:
        model = Organization
        fields = ["uuid", "name", "email", "timezone", "created_at", "updated_at", "user_role", "can_edit"]

    def get_user_role(self, obj):
        """Get the current user's role in this organization."""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            try:
                org_user = OrganizationUser.objects.get(user=request.user, organization=obj)
                return org_user.role
            except OrganizationUser.DoesNotExist:
                return None
        return None

    def get_can_edit(self, obj):
        """Check if the current user can edit this organization."""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            try:
                org_user = OrganizationUser.objects.get(user=request.user, organization=obj)
                return org_user.role in [OrganizationUser.RoleChoices.OWNER, OrganizationUser.RoleChoices.ADMIN]
            except OrganizationUser.DoesNotExist:
                return False
        return False

    def get_organization_timezone(self):
        """Override to get timezone from the organization instance itself."""
        if self.instance:
            return getattr(self.instance, 'timezone', 'UTC')
        return 'UTC'

    def to_representation(self, instance):
        """Add organization timezone to context before serialization."""
        if 'organization_timezone' not in self.context:
            self.context['organization_timezone'] = getattr(instance, 'timezone', 'UTC')
        return super().to_representation(instance)


class OrganizationConnectionSerializer(ModelSerializer):
    connections = BasicConnectionSerializer(read_only=True, many=True, source="connection_set")

    class Meta:
        model = Organization
        fields = ["uuid", "name", "connections"]
