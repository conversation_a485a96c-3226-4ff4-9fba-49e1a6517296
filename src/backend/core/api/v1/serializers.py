from rest_framework.serializers import ModelSerializer, ValidationError

from connections.api.v1.serializers import BasicConnectionSerializer
from core.models import Organization, Project
from core.timezone_utils import validate_timezone


class BasicProjectSerializer(ModelSerializer):
    class Meta:
        model = Project
        fields = ["uuid", "name", "created_at", "updated_at"]


class BasicOrganizationSerializer(ModelSerializer):
    class Meta:
        model = Organization
        fields = ["uuid", "name", "email", "timezone", "created_at", "updated_at"]


class OrganizationUpdateSerializer(ModelSerializer):
    class Meta:
        model = Organization
        fields = ["name", "email", "timezone"]

    def validate_timezone(self, value):
        """Validate that the timezone is a valid timezone name."""
        if not validate_timezone(value):
            raise ValidationError(f"'{value}' is not a valid timezone.")
        return value


class OrganizationConnectionSerializer(ModelSerializer):
    connections = BasicConnectionSerializer(read_only=True, many=True, source="connection_set")

    class Meta:
        model = Organization
        fields = ["uuid", "name", "connections"]
