from django.urls import path

from core.api.v1.views import (
    AppInfoView,
    TimezonesView,
    OrganizationListView,
    OrganizationProfileListView,
    OrganizationDescriptionView,
    OrganizationConnectionView,
    OrganizationUpdateView,
    ProjectDescriptionView,
    ProjectCreateFlowView,
    ProjectListView,
)

app_name = "core"

urlpatterns = [
    path("app-info/", AppInfoView.as_view(), name="app-info"),
    path("timezones/", TimezonesView.as_view(), name="timezones"),
    path(
        "organizations/list/", OrganizationListView.as_view(), name="organization-list"
    ),
    path(
        "organizations/profile-list/", OrganizationProfileListView.as_view(), name="organization-profile-list"
    ),
    path(
        "organizations/retrieve/<uuid:uuid>/",
        OrganizationDescriptionView.as_view(),
        name="organization-description",
    ),
    path(
        "organizations/retrieve-connections/<uuid:uuid>/",
        OrganizationConnectionView.as_view(),
        name="organization-connections",
    ),
    path(
        "organizations/update/<uuid:uuid>/",
        OrganizationUpdateView.as_view(),
        name="organization-update",
    ),
    path(
        "projects/retrieve/<uuid:uuid>/",
        ProjectDescriptionView.as_view(),
        name="project-description",
    ),
    path(
        "projects/create-flow/<uuid:uuid>/",
        ProjectCreateFlowView.as_view(),
        name="project-create-flow",
    ),
    path("projects/list/", ProjectListView.as_view(), name="project-list"),
]
