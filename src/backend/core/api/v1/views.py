from django.shortcuts import get_object_or_404
from django.core.exceptions import PermissionDenied

from rest_framework import status
from rest_framework.generics import GenericAPIView, ListAPIView, RetrieveAPIView, UpdateAPIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from drf_spectacular.utils import (
    extend_schema_view,
    extend_schema,
    OpenApiParameter,
    OpenApiResponse,
)
from drf_spectacular.types import OpenApiTypes

from core.api.v1.serializers import (
    BasicProjectSerializer,
    BasicOrganizationSerializer,
    OrganizationConnectionSerializer,
    OrganizationUpdateSerializer,
    OrganizationWithRoleSerializer,
)
from core.mixins import FilterByUserProjectsMixin, FilterByUserOrganizationsMixin
from core.models import Organization, OrganizationUser
from core.timezone_utils import get_timezones_for_ui

from flows.models import Flow

from symmy.utils import get_app_version


class AppInfoView(GenericAPIView):

    def get(self, request):
        data = {"version": get_app_version()}
        return Response(data, status=status.HTTP_200_OK)


@extend_schema_view(
    get=extend_schema(
        operation_id="Get Available Timezones",
        summary="Get list of available timezones for organization settings.",
        description="Returns a list of common timezones with display names and offsets.",
        responses={
            200: OpenApiResponse(description="List of available timezones"),
        },
    )
)
class TimezonesView(GenericAPIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get available timezones for UI selection."""
        timezones = get_timezones_for_ui()
        return Response(timezones, status=status.HTTP_200_OK)


@extend_schema_view(
    get=extend_schema(
        operation_id="Basic Project info",
        summary="Get basic information for a single Project.",
        parameters=[
            OpenApiParameter(
                name="uuid",
                type=OpenApiTypes.UUID,
                location=OpenApiParameter.PATH,
                description="Project UUID Identifier",
            )
        ],
    )
)
class ProjectDescriptionView(FilterByUserProjectsMixin, RetrieveAPIView):
    permission_classes = [
        IsAuthenticated,
    ]
    lookup_field = "uuid"
    serializer_class = BasicProjectSerializer


@extend_schema_view(
    post=extend_schema(
        operation_id="Create Flow",
        summary="Creates a new Flow for a specific Project within an Organization",
        responses={
            201: OpenApiResponse(description="Flow created"),
            404: OpenApiResponse(description="Project not found"),
        },
    )
)
class ProjectCreateFlowView(FilterByUserProjectsMixin, GenericAPIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, uuid):
        queryset = self.get_queryset()

        project = get_object_or_404(queryset, uuid=uuid)
        flow = Flow.objects.create(project=project, name="Flow")

        return Response(flow.uuid, status=status.HTTP_201_CREATED)


@extend_schema_view(
    get=extend_schema(
        operation_id="Get basic information for Projects.",
        summary="Returns Project uuid and name.",
        description="If project_uuids parameter is specified, returns only the Projects with specified uuids."
        " If organization_uuids parameter is specified, returns only the Projects related to these organizations.",
        parameters=[
            OpenApiParameter(
                name="project_uuids",
                type=OpenApiTypes.UUID,
                many=True,
                location=OpenApiParameter.QUERY,
                description="Project model uuids",
            ),
            OpenApiParameter(
                name="organization_uuids",
                type=OpenApiTypes.UUID,
                many=True,
                location=OpenApiParameter.QUERY,
                description="Organization model uuids",
            ),
        ],
        responses={
            200: BasicProjectSerializer,
            404: OpenApiResponse(description="Projects not found"),
        },
    )
)
class ProjectListView(FilterByUserProjectsMixin, ListAPIView):
    permission_classes = [
        IsAuthenticated,
    ]
    lookup_field = "uuid"
    serializer_class = BasicProjectSerializer

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()

        project_uuids = request.query_params.getlist("project_uuids")
        if project_uuids:
            queryset = queryset.filter(uuid__in=project_uuids)

        organization_uuids = request.query_params.getlist("organization_uuids")
        if organization_uuids:
            queryset = queryset.filter(organization__uuid__in=organization_uuids)

        serializer = BasicProjectSerializer(queryset, many=True)
        return Response(serializer.data)


@extend_schema_view(
    get=extend_schema(
        operation_id="Basic Organization info",
        summary="Get basic information for a single Organization.",
        parameters=[
            OpenApiParameter(
                name="uuid",
                type=OpenApiTypes.UUID,
                location=OpenApiParameter.PATH,
                description="Organization UUID Identifier",
            )
        ],
    )
)
class OrganizationDescriptionView(FilterByUserOrganizationsMixin, RetrieveAPIView):
    permission_classes = [
        IsAuthenticated,
    ]
    lookup_field = "uuid"
    serializer_class = BasicOrganizationSerializer


@extend_schema_view(
    get=extend_schema(
        operation_id="Organization's Connections",
        summary="Get information about all Connections defined by this Organization.",
    )
)
class OrganizationConnectionView(FilterByUserOrganizationsMixin, RetrieveAPIView):
    permission_classes = [
        IsAuthenticated,
    ]
    lookup_field = "uuid"
    serializer_class = OrganizationConnectionSerializer


@extend_schema_view(
    patch=extend_schema(
        operation_id="Update Organization",
        summary="Update organization settings including timezone.",
        description="Only organization owners and admins can update organization settings.",
        parameters=[
            OpenApiParameter(
                name="uuid",
                type=OpenApiTypes.UUID,
                location=OpenApiParameter.PATH,
                description="Organization UUID Identifier",
            )
        ],
        responses={
            200: OrganizationUpdateSerializer,
            400: OpenApiResponse(description="Invalid data"),
            403: OpenApiResponse(description="Permission denied"),
            404: OpenApiResponse(description="Organization not found"),
        },
    )
)
class OrganizationUpdateView(FilterByUserOrganizationsMixin, UpdateAPIView):
    permission_classes = [IsAuthenticated]
    lookup_field = "uuid"
    serializer_class = OrganizationUpdateSerializer
    http_method_names = ["patch"]

    def get_object(self):
        """Get organization and check if user has admin/owner permissions."""
        queryset = self.get_queryset()
        organization = get_object_or_404(queryset, uuid=self.kwargs["uuid"])

        # Check if user is owner or admin of this organization
        try:
            org_user = OrganizationUser.objects.get(
                user=self.request.user,
                organization=organization
            )
            if org_user.role not in [OrganizationUser.RoleChoices.OWNER, OrganizationUser.RoleChoices.ADMIN]:
                raise PermissionDenied("Only organization owners and admins can update organization settings.")
        except OrganizationUser.DoesNotExist:
            raise PermissionDenied("You are not a member of this organization.")

        return organization

    def partial_update(self, request, *args, **kwargs):
        response = super().partial_update(request, *args, **kwargs)
        return Response(response.data, status=status.HTTP_200_OK)


@extend_schema_view(
    get=extend_schema(
        operation_id="Get basic information for Organizations",
        summary="Returns Organization uuid and name.",
        description="If organization_uuids parameter is specified, "
        "returns only the Organizations with the specified uuids.",
        parameters=[
            OpenApiParameter(
                name="organization_uuids",
                type=OpenApiTypes.UUID,
                many=True,
                location=OpenApiParameter.QUERY,
                description="Organization model uuids",
            )
        ],
        responses={
            200: BasicOrganizationSerializer,
            404: OpenApiResponse(description="Organizations not found"),
        },
    )
)
class OrganizationListView(FilterByUserOrganizationsMixin, ListAPIView):
    permission_classes = [
        IsAuthenticated,
    ]
    lookup_field = "uuid"
    serializer_class = BasicOrganizationSerializer

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()

        organization_uuids = request.query_params.getlist("organization_uuids")
        if organization_uuids:
            queryset = queryset.filter(uuid__in=organization_uuids)

        serializer = BasicOrganizationSerializer(queryset, many=True)
        return Response(serializer.data)


@extend_schema_view(
    get=extend_schema(
        operation_id="Get Organizations with User Roles",
        summary="Returns Organizations with user role information for profile page.",
        description="Returns organizations the user is a member of, including their role and edit permissions.",
        responses={
            200: OrganizationWithRoleSerializer,
            404: OpenApiResponse(description="Organizations not found"),
        },
    )
)
class OrganizationProfileListView(FilterByUserOrganizationsMixin, ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = OrganizationWithRoleSerializer

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = OrganizationWithRoleSerializer(
            queryset,
            many=True,
            context={'request': request}
        )
        return Response(serializer.data)
