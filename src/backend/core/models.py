import logging
import uuid

from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _

from core.timezone_utils import get_common_timezones

logger = logging.getLogger("symmy")


class SymmyBaseModel(models.Model):
    uuid = models.UUIDField(
        _("UUID"), default=uuid.uuid4, editable=False, unique=True, db_index=True
    )

    created_at = models.DateTimeField(_("Created At"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Updated At"), auto_now=True)

    class Meta:
        abstract = True


class Organization(SymmyBaseModel):
    name = models.CharField(max_length=255, verbose_name=_("Name"))
    slug = models.SlugField(
        max_length=255, verbose_name=_("Slug"), unique=True, null=True
    )
    email = models.EmailField()
    timezone = models.CharField(
        max_length=50,
        verbose_name=_("Timezone"),
        choices=[(tz, tz) for tz in get_common_timezones()],
        default="UTC",
        help_text=_("Organization's preferred timezone")
    )

    class Meta:
        verbose_name = _("Organization")
        verbose_name_plural = _("Organizations")

    def __str__(self):
        return self.name


class OrganizationUser(SymmyBaseModel):
    class RoleChoices(models.TextChoices):
        OWNER = "owner", _("Owner")
        ADMIN = "admin", _("Admin")
        MEMBER = "member", _("Member")

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name=_("User")
    )
    organization = models.ForeignKey(
        Organization, on_delete=models.CASCADE, verbose_name=_("Organization")
    )
    role = models.CharField(
        _("Role"),
        choices=RoleChoices.choices,
        max_length=255,
        default=RoleChoices.MEMBER,
    )

    class Meta:
        verbose_name = _("Organization User")
        verbose_name_plural = _("Organization Users")
        unique_together = ("user", "organization")

    def __str__(self):
        return f"{self.user} in {self.organization}"


class ExtendedBaseModel(SymmyBaseModel):
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name=_("User")
    )
    organization = models.ForeignKey(
        Organization, on_delete=models.CASCADE, verbose_name=_("Organization")
    )

    class Meta:
        abstract = True


class Project(ExtendedBaseModel):
    name = models.CharField(max_length=255, verbose_name=_("Name"))

    class Meta:
        verbose_name = _("Project")
        verbose_name_plural = _("Projects")

    def __str__(self):
        return self.name
