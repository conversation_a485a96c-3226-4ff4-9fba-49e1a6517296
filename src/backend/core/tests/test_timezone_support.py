"""
Tests for timezone support functionality.
"""

from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from datetime import datetime

from core.models import Organization, OrganizationUser
from core.timezone_utils import (
    validate_timezone,
    convert_utc_to_timezone,
    convert_timezone_to_utc,
    get_timezones_for_ui,
    get_timezone_display_name,
)

User = get_user_model()


class TimezoneUtilsTestCase(TestCase):
    """Test timezone utility functions."""

    def test_validate_timezone(self):
        """Test timezone validation."""
        # Valid timezones
        self.assertTrue(validate_timezone('UTC'))
        self.assertTrue(validate_timezone('America/New_York'))
        self.assertTrue(validate_timezone('Europe/London'))
        self.assertTrue(validate_timezone('Asia/Tokyo'))
        
        # Invalid timezones
        self.assertFalse(validate_timezone('Invalid/Timezone'))
        self.assertFalse(validate_timezone('Not_A_Timezone'))
        self.assertFalse(validate_timezone(''))

    def test_convert_utc_to_timezone(self):
        """Test UTC to timezone conversion."""
        utc_time = timezone.make_aware(datetime(2023, 6, 15, 12, 0, 0), timezone.utc)
        
        # Convert to New York time (EDT in June)
        ny_time = convert_utc_to_timezone(utc_time, 'America/New_York')
        self.assertIsNotNone(ny_time)
        self.assertEqual(ny_time.hour, 8)  # 12 UTC - 4 hours EDT = 8 AM
        
        # Convert to London time (BST in June)
        london_time = convert_utc_to_timezone(utc_time, 'Europe/London')
        self.assertIsNotNone(london_time)
        self.assertEqual(london_time.hour, 13)  # 12 UTC + 1 hour BST = 1 PM

    def test_convert_timezone_to_utc(self):
        """Test timezone to UTC conversion."""
        # Create a naive datetime and convert from NY timezone
        local_time = datetime(2023, 6, 15, 8, 0, 0)  # 8 AM in NY
        utc_time = convert_timezone_to_utc(local_time, 'America/New_York')
        
        self.assertIsNotNone(utc_time)
        self.assertEqual(utc_time.hour, 12)  # 8 AM EDT + 4 hours = 12 UTC

    def test_get_timezones_for_ui(self):
        """Test getting timezones formatted for UI."""
        timezones = get_timezones_for_ui()
        
        self.assertIsInstance(timezones, list)
        self.assertGreater(len(timezones), 0)
        
        # Check structure of first timezone
        first_tz = timezones[0]
        self.assertIn('value', first_tz)
        self.assertIn('label', first_tz)
        self.assertIn('offset', first_tz)
        
        # UTC should be first
        self.assertEqual(first_tz['value'], 'UTC')

    def test_get_timezone_display_name(self):
        """Test timezone display name generation."""
        utc_display = get_timezone_display_name('UTC')
        self.assertIn('UTC', utc_display)
        
        ny_display = get_timezone_display_name('America/New_York')
        self.assertIn('America/New York', ny_display)
        self.assertIn('(', ny_display)  # Should contain timezone abbreviation


class OrganizationTimezoneTestCase(TestCase):
    """Test organization timezone functionality."""

    def setUp(self):
        self.organization = Organization.objects.create(
            name="Test Org",
            email="<EMAIL>",
            timezone="UTC"
        )

    def test_organization_default_timezone(self):
        """Test that organizations have UTC as default timezone."""
        org = Organization.objects.create(
            name="New Org",
            email="<EMAIL>"
        )
        self.assertEqual(org.timezone, "UTC")

    def test_organization_timezone_update(self):
        """Test updating organization timezone."""
        self.organization.timezone = "America/New_York"
        self.organization.save()
        
        # Refresh from database
        self.organization.refresh_from_db()
        self.assertEqual(self.organization.timezone, "America/New_York")


class TimezoneAPITestCase(APITestCase):
    """Test timezone-related API endpoints."""

    def setUp(self):
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Test"
        )
        self.organization = Organization.objects.create(
            name="Test Org",
            email="<EMAIL>",
            timezone="UTC"
        )
        OrganizationUser.objects.create(
            user=self.user,
            organization=self.organization,
            role=OrganizationUser.RoleChoices.OWNER
        )
        self.client.force_authenticate(user=self.user)

    def test_get_timezones_endpoint(self):
        """Test the timezones API endpoint."""
        response = self.client.get('/api/v1/core/timezones/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsInstance(response.data, list)
        self.assertGreater(len(response.data), 0)
        
        # Check structure
        first_tz = response.data[0]
        self.assertIn('value', first_tz)
        self.assertIn('label', first_tz)
        self.assertIn('offset', first_tz)

    def test_update_organization_timezone(self):
        """Test updating organization timezone via API."""
        url = f'/api/v1/core/organizations/update/{self.organization.uuid}/'
        data = {
            'name': self.organization.name,
            'email': self.organization.email,
            'timezone': 'America/New_York'
        }
        
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify timezone was updated
        self.organization.refresh_from_db()
        self.assertEqual(self.organization.timezone, 'America/New_York')

    def test_update_organization_invalid_timezone(self):
        """Test updating organization with invalid timezone."""
        url = f'/api/v1/core/organizations/update/{self.organization.uuid}/'
        data = {
            'name': self.organization.name,
            'email': self.organization.email,
            'timezone': 'Invalid/Timezone'
        }
        
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('timezone', response.data)

    def test_update_organization_permission_denied(self):
        """Test that non-admin users cannot update organization timezone."""
        # Create a regular member
        regular_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Regular"
        )
        OrganizationUser.objects.create(
            user=regular_user,
            organization=self.organization,
            role=OrganizationUser.RoleChoices.MEMBER
        )
        
        self.client.force_authenticate(user=regular_user)
        
        url = f'/api/v1/core/organizations/update/{self.organization.uuid}/'
        data = {
            'name': self.organization.name,
            'email': self.organization.email,
            'timezone': 'America/New_York'
        }
        
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
