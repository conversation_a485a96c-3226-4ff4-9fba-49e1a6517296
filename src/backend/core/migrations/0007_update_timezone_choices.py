# Generated by Django 4.2.2 on 2025-05-30 10:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0006_add_organization_timezone'),
    ]

    operations = [
        migrations.<PERSON>er<PERSON>ield(
            model_name='organization',
            name='timezone',
            field=models.CharField(choices=[('Europe/Prague', 'Europe/Prague'), ('America/New York', 'America/New_York'), ('America/Chicago', 'America/Chicago'), ('America/Denver', 'America/Denver'), ('America/Los Angeles', 'America/Los_Angeles'), ('America/Toronto', 'America/Toronto'), ('America/Vancouver', 'America/Vancouver'), ('Europe/London', 'Europe/London'), ('Europe/Paris', 'Europe/Paris'), ('Europe/Berlin', 'Europe/Berlin'), ('Europe/Rome', 'Europe/Rome'), ('Europe/Madrid', 'Europe/Madrid'), ('Europe/Amsterdam', 'Europe/Amsterdam'), ('Europe/Stockholm', 'Europe/Stockholm'), ('Europe/Zurich', 'Europe/Zurich'), ('Asia/Tokyo', 'Asia/Tokyo'), ('Asia/Shanghai', 'Asia/Shanghai'), ('Asia/Hong Kong', 'Asia/Hong_Kong'), ('Asia/Singapore', 'Asia/Singapore'), ('Asia/Seoul', 'Asia/Seoul'), ('Asia/Mumbai', 'Asia/Mumbai'), ('Asia/Dubai', 'Asia/Dubai'), ('Australia/Sydney', 'Australia/Sydney'), ('Australia/Melbourne', 'Australia/Melbourne'), ('Pacific/Auckland', 'Pacific/Auckland'), ('UTC', 'UTC')], default='UTC', help_text="Organization's preferred timezone", max_length=50, verbose_name='Timezone'),
        ),
    ]
