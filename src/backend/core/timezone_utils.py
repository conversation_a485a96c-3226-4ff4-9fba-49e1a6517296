"""
Timezone utilities for converting UTC datetimes to organization-specific timezones.
Uses the standard library zoneinfo module (Python 3.9+).
"""

import logging
from datetime import datetime
from typing import Optional, List
from zoneinfo import ZoneInfo, available_timezones

from django.utils import timezone

logger = logging.getLogger("symmy")


def get_available_timezones() -> List[str]:
    """
    Get a list of all available timezone names.
    
    Returns:
        List of timezone names sorted alphabetically
    """
    return sorted(list(available_timezones()))


def get_common_timezones() -> List[str]:
    """
    Get a list of commonly used timezone names for UI selection.
    
    Returns:
        List of common timezone names
    """
    common_timezones = [
        "UTC",
        "America/New_York",
        "America/Chicago", 
        "America/Denver",
        "America/Los_Angeles",
        "America/Toronto",
        "America/Vancouver",
        "Europe/London",
        "Europe/Paris",
        "Europe/Berlin",
        "Europe/Rome",
        "Europe/Madrid",
        "Europe/Amsterdam",
        "Europe/Stockholm",
        "Europe/Zurich",
        "Asia/Tokyo",
        "Asia/Shanghai",
        "Asia/Hong_Kong",
        "Asia/Singapore",
        "Asia/Seoul",
        "Asia/Mumbai",
        "Asia/Dubai",
        "Australia/Sydney",
        "Australia/Melbourne",
        "Pacific/Auckland",
    ]
    return common_timezones


def validate_timezone(timezone_name: str) -> bool:
    """
    Validate if a timezone name is valid.
    
    Args:
        timezone_name: The timezone name to validate
        
    Returns:
        True if valid, False otherwise
    """
    try:
        ZoneInfo(timezone_name)
        return True
    except Exception:
        return False


def convert_utc_to_timezone(utc_datetime: datetime, target_timezone: str) -> Optional[datetime]:
    """
    Convert a UTC datetime to the specified timezone.
    
    Args:
        utc_datetime: UTC datetime to convert (should be timezone-aware)
        target_timezone: Target timezone name (e.g., 'America/New_York')
        
    Returns:
        Converted datetime in target timezone, or None if conversion fails
    """
    if not utc_datetime:
        return None
        
    try:
        # Ensure the datetime is timezone-aware and in UTC
        if utc_datetime.tzinfo is None:
            utc_datetime = timezone.make_aware(utc_datetime, timezone.utc)
        elif utc_datetime.tzinfo != timezone.utc:
            utc_datetime = utc_datetime.astimezone(timezone.utc)
            
        # Convert to target timezone
        target_tz = ZoneInfo(target_timezone)
        converted_datetime = utc_datetime.astimezone(target_tz)
        
        return converted_datetime
        
    except Exception as e:
        logger.warning(f"Failed to convert datetime to timezone {target_timezone}: {e}")
        return utc_datetime  # Return original datetime if conversion fails


def convert_timezone_to_utc(local_datetime: datetime, source_timezone: str) -> Optional[datetime]:
    """
    Convert a datetime from a specific timezone to UTC.
    
    Args:
        local_datetime: Datetime in the source timezone
        source_timezone: Source timezone name (e.g., 'America/New_York')
        
    Returns:
        Converted datetime in UTC, or None if conversion fails
    """
    if not local_datetime:
        return None
        
    try:
        # If datetime is naive, assume it's in the source timezone
        if local_datetime.tzinfo is None:
            source_tz = ZoneInfo(source_timezone)
            local_datetime = local_datetime.replace(tzinfo=source_tz)
            
        # Convert to UTC
        utc_datetime = local_datetime.astimezone(timezone.utc)
        
        return utc_datetime
        
    except Exception as e:
        logger.warning(f"Failed to convert datetime from timezone {source_timezone} to UTC: {e}")
        return local_datetime  # Return original datetime if conversion fails


def format_datetime_for_timezone(utc_datetime: datetime, target_timezone: str, format_string: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    Format a UTC datetime for display in a specific timezone.
    
    Args:
        utc_datetime: UTC datetime to format
        target_timezone: Target timezone name
        format_string: Python datetime format string
        
    Returns:
        Formatted datetime string in target timezone
    """
    converted_datetime = convert_utc_to_timezone(utc_datetime, target_timezone)
    
    if converted_datetime:
        return converted_datetime.strftime(format_string)
    else:
        # Fallback to original datetime if conversion fails
        return utc_datetime.strftime(format_string) if utc_datetime else ""


def get_timezone_display_name(timezone_name: str) -> str:
    """
    Get a human-readable display name for a timezone.
    
    Args:
        timezone_name: Timezone name (e.g., 'America/New_York')
        
    Returns:
        Human-readable timezone display name
    """
    try:
        # Create a datetime to get the timezone abbreviation
        now = datetime.now(ZoneInfo(timezone_name))
        tz_abbrev = now.strftime('%Z')
        
        # Format as "Region/City (ABBREV)"
        if '/' in timezone_name:
            region, city = timezone_name.split('/', 1)
            city = city.replace('_', ' ')
            return f"{region}/{city} ({tz_abbrev})"
        else:
            return f"{timezone_name} ({tz_abbrev})"
            
    except Exception:
        return timezone_name


def get_timezone_offset(timezone_name: str) -> str:
    """
    Get the current UTC offset for a timezone.
    
    Args:
        timezone_name: Timezone name
        
    Returns:
        UTC offset string (e.g., '+05:30', '-08:00')
    """
    try:
        now = datetime.now(ZoneInfo(timezone_name))
        offset = now.strftime('%z')
        # Format as +HH:MM
        if len(offset) == 5:
            return f"{offset[:3]}:{offset[3:]}"
        return offset
    except Exception:
        return "+00:00"


def get_timezones_for_ui() -> List[dict]:
    """
    Get timezone data formatted for UI components (dropdowns, etc.).
    
    Returns:
        List of dictionaries with timezone information for UI
    """
    common_timezones = get_common_timezones()
    timezone_data = []
    
    for tz_name in common_timezones:
        timezone_data.append({
            'value': tz_name,
            'label': get_timezone_display_name(tz_name),
            'offset': get_timezone_offset(tz_name),
        })
    
    return timezone_data
