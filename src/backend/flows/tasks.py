import logging
import re
from datetime import <PERSON><PERSON><PERSON>
from enum import Enum
from typing import Any

from celery import group, shared_task
from celery.exceptions import (
    MaxRetriesExceededError,
    Retry,
    SoftTimeLimitExceeded,
    TimeLimitExceeded,
)
from django.conf import settings
from django.utils import timezone
from symmy_nodetype.exceptions import ConnectionError, AuthenticationError, RetryError

from core.exceptions import ImproperlyConfigured
from flows.evaluate import evaluate
from flows.log_factory import flow_msg, node_msg
from flows.models import Flow, Node
from flows.utils import (
    handle_exception_in_task,
    FlowExecutionLock,
    FlowExecutionLockError,
)
from mongo.operations import (
    store_flow_data,
    FlowLogOperations,
    NodeCallLogOperations,
    GridFSOperations,
)
from mongo.types import GridFSDataType, Status
from mongo.utils import status_as_dict
from symmy.celery import app

logger = logging.getLogger("symmy")


class TaskStatus(Enum):
    retry_needed = 0
    fail = 1
    success = 2


class FlowExecutionError(Exception):
    """Raised when flow execution encounters an error."""

    pass


class FlowTimeoutError(FlowExecutionError):
    """Raised when flow execution exceeds time limit."""

    pass


@app.task
def execute_flow(
    flow: Flow | int,
    retry_backoff: int = settings.CELERY_TASK_RETRY_BACKOFF,
) -> Any:
    """
    Starts flow execution with time limit and single instance constraint.

    Args:
        flow (flows.models.Flow): Flow to execute
        retry_backoff (int): The number of seconds by which the retry time for task retries will increment

    Returns:
        str: MongoDB flow ID if execution started successfully

    Raises:
        FlowExecutionLockError: If another instance of this flow is already running
        ImproperlyConfigured: If root node is missing required resources
    """
    if isinstance(flow, int):
        flow = Flow.objects.get(pk=flow)

    # Create flow log first to get mongo_flow_id
    flow_log_operations = FlowLogOperations(flow_instance=flow)
    flow_log_operations.set_params(status=Status.in_progress, start=timezone.now())
    mongo_flow_id = str(flow_log_operations.create_log().inserted_id)

    # Check if another instance of this flow is already running
    # Pass mongo_flow_id and task_id for enhanced lock tracking
    current_task_id = (
        execute_flow.request.id if hasattr(execute_flow, "request") else "unknown"
    )
    flow_lock = FlowExecutionLock(
        flow.id,
        timeout=1200,  # 20 minutes
        mongo_flow_id=mongo_flow_id,
        task_id=current_task_id,
    )

    if not flow_lock.acquire(blocking=False):
        error_msg = f"Flow {flow.name} ({flow.uuid}) is already running. Only one instance per flow is allowed."
        logger.warning(error_msg)

        # Update the existing flow log to skipped status
        try:
            current_time = timezone.now()
            flow_log_operations.set_params(status=Status.skipped, end=current_time)
            flow_log_operations.update_log()
            logger.info(
                f"Updated flow log {mongo_flow_id} to skipped status for concurrent execution attempt"
            )
        except Exception as log_error:
            logger.error(
                f"Failed to update flow log for concurrent execution: {log_error}"
            )

        # Return error message instead of raising exception to avoid .get() issues
        return {"error": error_msg, "status": "skipped", "mongo_flow_id": mongo_flow_id}

    try:
        root = flow.root

        if not (root.node_type_resource or root.action_resource):
            raise ImproperlyConfigured(
                f"Root Node is missing node_type_resource or action_resource"
            )

        msg = f"Executing flow {flow.name} with 20-minute time limit"
        logger.info(msg)
        logger.debug(flow_msg(flow, Status.in_progress))

        # Register flow completion callback to ensure lock is released when flow completes
        from flows.utils import register_flow_completion_callback

        register_flow_completion_callback(mongo_flow_id)

        # Start execution with 20-minute time limit (1200 seconds)
        execute_tasks.apply_async(
            kwargs={
                "mongo_flow_id": mongo_flow_id,
                "node_uuid": root.uuid,
                "input_data_dict": {},
                "retry_backoff": retry_backoff,
                "flow_id": flow.id,  # Pass flow ID for lock management
            },
            expires=1200,  # 20 minutes (1200 seconds)
        )
        return mongo_flow_id

    except Exception as e:
        # Release lock if there's an error during setup
        flow_lock.release()
        raise


class TaskLog:
    """
    Class which handles internal and mongo logging during the Node Action execution.
    """

    error_msgs = {
        "raw_data": "Could not delete old raw data on node execution retry.",
        "node_data": "Could not store node data.",
        "flow_data": "Could not store flow data.",
        "output": "Could not construct output data.",
        "transformation": "Could not transform data.",
        "child_nodes": "Could not execute child Nodes.",
    }

    def __init__(self, node_task: "NodeTask"):
        self.node_task = node_task
        self.flow_log_operations = node_task.flow_log_operations
        self.node_call_log_operations = node_task.node_call_log_operations
        self.curr_node_action = f"{node_task.node.node_instance.name}: {node_task.node.action_resource.name}"

        self.node_call_log_operations.set_params(start=timezone.now())

    @staticmethod
    def _construct_error_string(errors: list[Exception]):
        res = ""
        for err in errors:
            res += str(err)

        return res

    def log_in_progress(self, msg):
        if msg is None:
            msg = f"{self.curr_node_action} in progress"
        logger.info(msg)
        logger.debug(node_msg(node=self.node_task.node, status=Status.in_progress))

        self.node_call_log_operations.set_params(
            status=Status.in_progress,
        )
        self.node_call_log_operations.update_log()

    def log_failed(self, msg_key: str = None, custom_msg: str = None):
        msg = custom_msg
        if msg is None:
            msg = (
                f"{self.curr_node_action} failed."
                if msg_key is None
                else TaskLog.error_msgs[msg_key]
            )

        if self.node_call_log_operations.errors:
            msg += (
                f"{self._construct_error_string(self.node_call_log_operations.errors)}"
            )

        logger.error(msg)
        logger.debug(node_msg(self.node_task.node, Status.failed))

        self.node_call_log_operations.set_params(
            status=Status.failed,
            end=timezone.now(),
        )
        self.node_call_log_operations.update_log()

    def log_successful(self, msg: str = None):
        if msg is None:
            msg = f"Successfully executed {self.curr_node_action}"
        logger.info(msg)
        logger.debug(node_msg(self.node_task.node, Status.success))
        self.node_call_log_operations.set_params(
            status=Status.success, end=timezone.now()
        )
        self.node_call_log_operations.update_log()

    def log_action_execution(self, msg: str = None):
        if self.node_call_log_operations.retries != 0:
            self.log_in_progress(msg)
        else:
            self.log_first_execution(msg)

    def log_first_execution(self, msg):
        if msg is None:
            msg = f"Executing {self.curr_node_action}"
        logger.info(msg)
        logger.debug(node_msg(node=self.node_task.node, status=Status.in_progress))

        self.node_call_log_operations.set_params(
            status=Status.in_progress,
        )

        if self.node_task.is_child_task:  # already created in mongo.operations.py
            self.node_call_log_operations.update_log()
        else:
            self.node_call_log_operations.create_log()


class NodeTask:
    """
    Class which handles the Node Action execution.
    """

    def __init__(
        self,
        node_instance: Node,
        input_data_dict: dict | None,
        flow_log_operations: FlowLogOperations,
        node_call_log_operations: NodeCallLogOperations,
        retry_backoff: int = settings.CELERY_TASK_RETRY_BACKOFF,
        is_child_task: bool = False,
    ):
        self.node = node_instance

        if input_data_dict:
            self.input_data_dict = input_data_dict
        else:
            self.input_data_dict = self.node.input
        self.output_data_dict = dict()

        self.flow_log_operations = flow_log_operations
        self.node_call_log_operations = node_call_log_operations

        self.retry_backoff: int = retry_backoff

        self.is_child_task = is_child_task

        self.task_logger = TaskLog(self)

    def _clear_raw_data_on_retry(self):
        with handle_exception_in_task(self, "raw_data"):
            if self.node_call_log_operations.retries > 0:
                GridFSOperations.delete_data(self.node_call_log_operations.raw_data)

    def run_node_action(self) -> TaskStatus:
        """
        Runs the whole process associated with executing an action.

        Returns:
            tuple[bool, bool]: First value signifies, whether the task failed or not.
                Second value, if the task should be retried.
        """
        self._clear_raw_data_on_retry()

        try:
            received_data, action_call = self.execute_action()
        except Exception as ex:
            return self.process_action_execution_exception(ex)

        self.process_data_created_during_execution(action_call, received_data)
        self.task_logger.log_successful()
        self.execute_children()
        return TaskStatus.success

    def process_action_execution_exception(self, exception):
        if getattr(exception, "action_call", None) is not None:
            action_call = exception.action_call
            self.node_call_log_operations.request_count += action_call.request_count
            errors, _ = action_call._get_errors_and_failed_action_calls()
            self.node_call_log_operations.errors.extend(errors)

        self.node_call_log_operations.errors.append(exception)

        if any(
            isinstance(ex, (ConnectionError, AuthenticationError, RetryError))
            for ex in self.node_call_log_operations.errors
        ):
            logger.error(str(exception))
            return TaskStatus.retry_needed
        else:
            logger.error("Task failed!\n")
            self.task_logger.log_failed()
            return TaskStatus.fail

    def process_data_created_during_execution(self, action_call, received_data):
        with handle_exception_in_task(self, "node_data"):
            # XXX: We decide to not store the raw data in the database due to performance issues.
            # raw_data = GridFSOperations.store_data(
            #     data=construct_raw_data(action_call),
            #     data_type=GridFSDataType.raw_data,
            # )
            raw_data = None
            self.node_call_log_operations.set_params(raw_data=raw_data)

            new_output_data_id = GridFSOperations.store_data(
                data=received_data,
                data_type=GridFSDataType.serializable_data,
            )
            if self.node_call_log_operations.output_data_ids is None:
                self.node_call_log_operations.set_params(
                    output_data_ids=[new_output_data_id]
                )
            else:
                self.node_call_log_operations.set_params(
                    output_data_ids=self.node_call_log_operations.output_data_ids
                    + [new_output_data_id]
                )

            self.output_data_dict = {**self.input_data_dict, **received_data}

    def execute_action(self):
        """
        Executes the action which is set on the node and returns the result.
        """
        self.task_logger.log_action_execution()

        transformed_data = self.transform_data()
        action_call = self.node.execute(transformed_data)
        self.node_call_log_operations.request_count += action_call.request_count

        logger.info("Successfully executed action")
        if action_call.is_datastore_changed:
            with handle_exception_in_task(self, "flow_data"):
                # Store data in flow_store collection
                store_flow_data(self.node.flow, action_call.store_data)

        with handle_exception_in_task(self, "output_data"):
            res = dict()
            if action_call.data:
                for key, val in action_call.data.items():
                    res[f"{self.node.uuid}.{key}"] = val

            return res, action_call

    def transform_data(self) -> dict:
        """
        Transforms the input data.

        Returns:
            dict: Transformed data
        """
        logger.info(f"Transforming data for {self.node.action_resource.name}")

        with handle_exception_in_task(self, "transformation"):
            return transform(self.input_data_dict, self.node)

    def execute_children(self):
        """
        Starts execute_tasks task for child nodes if there are any.
        """
        with handle_exception_in_task(self, "child_nodes"):
            if len(self.node.children) != 0:
                logger.info(
                    f"Executing children of node {self.node.node_instance.name}"
                )
                group(
                    execute_tasks.s(
                        mongo_flow_id=str(self.flow_log_operations.mongo_flow_id),
                        node_uuid=child.uuid,
                        input_data_dict=self.output_data_dict,
                        input_data_ids=[
                            str(id_)
                            for id_ in self.node_call_log_operations.output_data_ids
                        ],
                        retry_backoff=self.retry_backoff,
                        is_child_task=True,
                        flow_id=None,  # Child tasks don't manage the flow lock
                    )
                    for child in self.node.children
                ).apply_async()


@shared_task(
    bind=True,
    max_retries=settings.CELERY_TASK_MAX_RETRIES,
    time_limit=1200,  # 20 minutes hard time limit
    soft_time_limit=1180,  # 19 minutes 40 seconds soft limit for cleanup
)
def execute_tasks(
    self,
    mongo_flow_id: str,
    node_uuid: int,
    input_data_dict: dict,
    retry_backoff: int,
    input_data_ids: list[str] = None,
    raw_data: str = None,
    retries: int = 0,
    request_count: int = 0,
    is_child_task: bool = False,
    flow_id: int = None,
):
    """
    Executes the action which is set on the node with node_uuid,
    passes its result to all of its children and starts their execution.

    This task has a 20-minute time limit and will handle flow execution lock cleanup.

    Args:
        node_uuid (int): uuid of the node.
        mongo_flow_id(str): str(_id) of current flow document in mongodb collection.
        input_data_dict (dict): data which is passed to the node's action.
        input_data_ids (list[str]): GridFS document ids which should be set in the log
            as the input data of this node call.
        raw_data (str): GridFS document id which corresponds to the raw data set by this Node Action.
            Should be set only if the task is being retried.
        retry_backoff (int): The number of seconds by which the retry time for task retries will increment.
        retries (int): The number of retries made.
        request_count (int): The amount of requests made by the action(for retry tasks).
        is_child_task (bool): If the task is a child of another one.
        flow_id (int): The Flow instance ID for lock management (only set for root tasks).
    """

    # Note: Flow execution lock is now managed at the flow level, not task level
    # This ensures locks are held for the entire flow lifecycle, including child tasks
    if flow_id and not is_child_task:
        logger.info(
            f"Root task starting for flow {flow_id} (lock managed at flow level)"
        )

    def create_not_started_node_call_failed_log(exc: Exception):
        strexc = str(exc)
        NodeCallLogOperations.create_node_call_manually(
            mongo_flow_id=mongo_flow_id,
            uuid=node_uuid,
            status=status_as_dict(Status.failed),
            start=timezone.now(),
            end=timezone.now(),
            errors=[strexc],
        )
        logger.error(strexc)

        # Trigger flow completion check since this node failed before execution
        try:
            flow_log_operations = FlowLogOperations(mongo_flow_id=mongo_flow_id)
            flow_log_operations._try_update_flow_final_status()
            logger.info(f"Triggered flow completion check after node {node_uuid} failed to start")
        except Exception as completion_error:
            logger.error(f"Error triggering flow completion check: {str(completion_error)}")

        # Note: Lock release is now handled by flow completion callback

    def cleanup_on_timeout():
        """Clean up resources when task times out."""
        logger.warning(f"Flow execution timed out for flow {flow_id}")

        # Update flow log status to timeout
        try:
            flow_log_operations = FlowLogOperations(mongo_flow_id=mongo_flow_id)
            flow_log_operations.set_params(status=Status.timeout, end=timezone.now())
            flow_log_operations.update_log()

            # Trigger flow completion callback to release lock
            from flows.utils import handle_flow_completion

            handle_flow_completion(mongo_flow_id, "timeout")
        except Exception as e:
            logger.error(f"Error updating flow log on timeout: {str(e)}")

    try:
        try:
            node_instance = Node.objects.get(uuid=node_uuid)
        except Exception as ex:
            create_not_started_node_call_failed_log(
                Exception("Could not retrieve current Node instance from db!", ex)
            )
            return

        try:
            flow_log_operations = FlowLogOperations(mongo_flow_id=mongo_flow_id)
        except Exception as ex:
            create_not_started_node_call_failed_log(
                Exception("Could not create FlowLogOperations instance!", ex)
            )
            return

        try:
            node_call_log_operations = NodeCallLogOperations(
                flow_log_operations=flow_log_operations,
                node_instance=node_instance,
            )
            node_call_log_operations.set_params(
                start=timezone.now(),
                status=Status.in_progress,
                input_data_ids=input_data_ids,
                raw_data=raw_data,
                output_data_ids=None,
                retries=retries,
                request_count=request_count,
                errors=[],
            )
        except Exception as ex:
            create_not_started_node_call_failed_log(
                Exception("Could not create NodeCallLogOperations instance!", ex)
            )
            return

        try:
            node_task_instance = NodeTask(
                node_instance=node_instance,
                input_data_dict=input_data_dict,
                flow_log_operations=flow_log_operations,
                node_call_log_operations=node_call_log_operations,
                retry_backoff=retry_backoff,
                is_child_task=is_child_task,
            )
        except Exception as ex:
            # Create a failed node call since NodeTask creation failed
            logger.info(f"Creating failed node call for node {node_uuid} due to NodeTask creation failure")
            node_call_log_operations.set_params(
                status=Status.failed,
                start=timezone.now(),
                end=timezone.now(),
                errors=[ex],
            )

            try:
                result = node_call_log_operations.create_log()  # Use create_log() since the node call doesn't exist yet
                logger.info(f"Successfully created failed node call for node {node_uuid}: {result}")
            except Exception as create_error:
                logger.error(f"Failed to create node call log for node {node_uuid}: {str(create_error)}")

            logger.error(f"Could not create NodeTask instance\n{str(ex)}")

            # Trigger flow completion check since this node failed
            try:
                flow_log_operations._try_update_flow_final_status()
                logger.info(f"Triggered flow completion check after NodeTask creation failed for node {node_uuid}")
            except Exception as completion_error:
                logger.error(f"Error triggering flow completion check: {str(completion_error)}")

            # Note: Lock release is now handled by flow completion callback
            return

        try:
            status: TaskStatus = node_task_instance.run_node_action()
            if status == TaskStatus.retry_needed:
                try:
                    self.retry(
                        kwargs={
                            "mongo_flow_id": mongo_flow_id,
                            "node_uuid": node_uuid,
                            "input_data_dict": input_data_dict,
                            "input_data_ids": input_data_ids,
                            "raw_data": node_task_instance.node_call_log_operations.raw_data,
                            "retry_backoff": retry_backoff,
                            "retries": retries + 1,
                            "request_count": node_task_instance.node_call_log_operations.request_count,
                            "flow_id": flow_id,  # Pass flow_id for retries
                        },
                        countdown=retry_backoff * (retries + 1),
                    )

                except MaxRetriesExceededError as ex:
                    # We don't put request_count here as the Action is not being executed
                    node_task_instance.node_call_log_operations.errors.append(ex)
                    node_task_instance.task_logger.log_failed(
                        custom_msg="Action exceeded the maximum amount of retries.\n"
                    )
                    # Note: Lock release is now handled by flow completion callback
                    return

            # Note: Lock release is now handled by flow completion callback when flow completes
            if not is_child_task:
                logger.info(
                    f"Root task completed for flow {flow_id} (lock will be released on flow completion)"
                )

        except Retry:
            logger.error(f"Retrying in {retries + 1}\n")
            raise  # celery implementation detail
        except Exception as ex:
            msg = f"Error occurred during the run node action phase!\n{str(ex)}\n"
            logger.error(msg)
            if "node_task_instance" in locals():
                node_task_instance.node_call_log_operations.errors.append(ex)
                node_task_instance.task_logger.log_failed(custom_msg=msg)

            # Note: Lock release is now handled by flow completion callback
            return

    except (SoftTimeLimitExceeded, TimeLimitExceeded) as ex:
        logger.error(f"Flow execution timed out after 20 minutes: {str(ex)}")
        cleanup_on_timeout()
        raise FlowTimeoutError("Flow execution exceeded 20-minute time limit")
    except Exception as ex:
        logger.error(f"Unexpected error in flow execution: {str(ex)}")
        # Note: Lock release is now handled by flow completion callback
        raise


def transform(data: dict, node) -> dict:
    """
    Transforms the data by replacing all uuids with their values and evaluating the expressions.

    Args:
        data (dict): data to be transformed
        node (flows.models.Node): node which is used to get the input

    Returns: transformed data
    """
    transformed_data = {}

    # Matches <uuid>.<key> pattern
    pattern = r"\b[0-9a-fA-F]{8}-?[0-9a-fA-F]{4}-?[0-9a-fA-F]{4}-?[0-9a-fA-F]{4}-?[0-9a-fA-F]{12}\b\.\w+\b"

    for key, value in node.input.items():
        if not isinstance(value, str) or len(value) < 3:
            transformed_data[key] = value
        else:
            matches = re.findall(pattern, value)
            value = replace_keys(matches, data, value)
            transformed_data[key] = evaluate(value) if isinstance(value, str) else value

    logger.info(f"Transformed data: {transformed_data}")
    return transformed_data


def replace_keys(matches: list, data: dict, value: str) -> str:
    """
    Replaces all matches in value with their values from data.

    Args:
        matches (list): list of matches
        data (dict): dict containing the values
        value (str): string to be transformed

    Returns: transformed string
    """

    for match in matches:
        data_match = data.get(match)
        if data_match is not None:
            if value == match:
                value = data_match  # No need to stringify, as there is no expression
                break

            if isinstance(data_match, str):
                data_match = f"'{data_match}'"  # Sympify needs strings to be quoted
            logger.info(f"Replacing {match} with {data_match} in {value}")

            value = re.sub(match, str(data_match), value)

    return value


@shared_task
def check_stuck_flows():
    """
    Periodic task to check for flows that are stuck in "in_progress" status
    and set their status to timeout if they exceed the expected execution time.

    This task should be run periodically (e.g., every 5-10 minutes) to catch
    flows that may have gotten stuck due to system issues, crashes, or other problems.
    """
    from mongo.operations import FlowLogOperations
    from flows.models import FlowExecutionLockRecord
    from flows.utils import handle_flow_completion
    from mongo.conf import flows

    logger.info("Starting check for stuck flows...")

    # Flows running longer than this are considered stuck
    # Using 30 minutes timeout threshold
    timeout_threshold = timezone.now() - timedelta(minutes=30)

    # Convert to naive datetime for MongoDB comparison (MongoDB stores naive datetimes)
    timeout_threshold_naive = timezone.make_naive(timeout_threshold)

    try:
        # Find flows that are in_progress and started more than 30 minutes ago
        stuck_flows_cursor = flows.find(
            {
                "status.code": Status.in_progress.value[0],
                "start": {"$lt": timeout_threshold_naive},
            }
        )

        stuck_flows = list(stuck_flows_cursor)
        stuck_count = len(stuck_flows)

        if stuck_count == 0:
            logger.info("No stuck flows found.")
            # Continue to check for orphaned locks even if no stuck flows found
            timed_out_count = 0
        else:
            logger.warning(f"Found {stuck_count} stuck flows that will be timed out.")

            timed_out_count = 0

            for flow_doc in stuck_flows:
                mongo_flow_id = str(flow_doc["_id"])
                flow_start_time = flow_doc.get("start")

                try:
                    # Handle timezone-aware/naive datetime comparison
                    if flow_start_time:
                        if flow_start_time.tzinfo is None:
                            # Make timezone-naive datetime timezone-aware
                            flow_start_time = timezone.make_aware(flow_start_time)
                        running_duration = timezone.now() - flow_start_time
                        duration_str = f", running for {running_duration}"
                    else:
                        duration_str = ""

                    logger.warning(
                        f"Timing out stuck flow {mongo_flow_id} "
                        f"(started at {flow_start_time}{duration_str})"
                    )

                    # Update flow status to timeout
                    flow_log_operations = FlowLogOperations(mongo_flow_id=mongo_flow_id)
                    flow_log_operations.set_params(
                        status=Status.timeout, end=timezone.now()
                    )
                    flow_log_operations.update_log()

                    # Trigger flow completion callback to release any associated locks
                    handle_flow_completion(mongo_flow_id, "timeout")

                    timed_out_count += 1
                    logger.info(f"Successfully timed out stuck flow {mongo_flow_id}")

                except Exception as e:
                    logger.error(
                        f"Error timing out stuck flow {mongo_flow_id}: {str(e)}"
                    )
                    continue

        # Also check for orphaned lock records (locks without corresponding in_progress flows)
        orphaned_locks_cleaned = 0
        try:
            # Find lock records that are expired or don't have corresponding in_progress flows
            expired_locks = FlowExecutionLockRecord.objects.filter(
                timeout_at__lt=timezone.now()
            )

            for lock_record in expired_locks:
                try:
                    # Check if there's still an in_progress flow for this lock
                    flow_doc = flows.find_one(
                        {
                            "_id": lock_record.mongo_flow_id,
                            "status.code": Status.in_progress.value[0],
                        }
                    )

                    if not flow_doc:
                        # No in_progress flow found, this is an orphaned lock
                        logger.warning(
                            f"Cleaning up orphaned lock record for flow {lock_record.flow.name}"
                        )
                        handle_flow_completion(lock_record.mongo_flow_id, "cleanup")
                        orphaned_locks_cleaned += 1

                except Exception as e:
                    logger.error(
                        f"Error checking/cleaning orphaned lock {lock_record.id}: {str(e)}"
                    )
                    continue

        except Exception as e:
            logger.error(f"Error during orphaned lock cleanup: {str(e)}")

        result = {
            "status": "success",
            "stuck_flows_found": stuck_count,
            "flows_timed_out": timed_out_count,
            "orphaned_locks_cleaned": orphaned_locks_cleaned,
        }

        logger.info(
            f"Stuck flow check completed: {timed_out_count}/{stuck_count} flows timed out, "
            f"{orphaned_locks_cleaned} orphaned locks cleaned"
        )

        return result

    except Exception as e:
        logger.error(f"Error during stuck flow check: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "stuck_flows_found": 0,
            "flows_timed_out": 0,
        }


@shared_task
def clear_expired_flow_locks():
    """
    Periodic task to clear expired flow execution locks.
    This is a backup cleanup mechanism in addition to the completion callbacks.
    """
    from flows.utils import clear_stale_flow_locks

    try:
        cleared_count = clear_stale_flow_locks()
        logger.info(f"Cleared {cleared_count} expired flow locks")
        return {"status": "success", "cleared_locks": cleared_count}
    except Exception as e:
        logger.error(f"Error clearing expired flow locks: {str(e)}")
        return {"status": "error", "error": str(e), "cleared_locks": 0}
